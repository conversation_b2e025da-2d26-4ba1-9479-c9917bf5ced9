// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameOnlineTemplate = "online_templates"

// OnlineTemplate mapped from table <online_templates>
type OnlineTemplate struct {
	ID            int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true" json:"id"`
	Name          string     `gorm:"column:name;type:character varying(100);not null;index:idx_online_templates_name,priority:1;comment:模板名称" json:"name"` // 模板名称
	Template      string     `gorm:"column:template;type:text;not null;comment:YAML配置文件内容，以字符串形式存储" json:"template"`                                       // YAML配置文件内容，以字符串形式存储
	CreatedAt     *time.Time `gorm:"column:created_at;type:timestamp with time zone;index:idx_online_templates_created_at,priority:1;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt     *time.Time `gorm:"column:updated_at;type:timestamp with time zone;default:CURRENT_TIMESTAMP" json:"updated_at"`
	Tags          *string    `gorm:"column:tags;type:text[];index:idx_online_templates_tags,priority:1;default:{};comment:模板标签数组" json:"tags"`                     // 模板标签数组
	CreatedBy     int32      `gorm:"column:created_by;type:integer;not null;index:idx_online_templates_created_by,priority:1;comment:模板创建者用户ID" json:"created_by"` // 模板创建者用户ID
	DownloadCount *int32     `gorm:"column:download_count;type:integer;index:idx_online_templates_download_count,priority:1;comment:模板下载次数" json:"download_count"` // 模板下载次数
	IsPublic      *bool      `gorm:"column:is_public;type:boolean;index:idx_online_templates_public,priority:1;default:true;comment:是否为公开模板" json:"is_public"`     // 是否为公开模板
	IsFeatured    *bool      `gorm:"column:is_featured;type:boolean;index:idx_online_templates_featured,priority:1;comment:是否为精选模板" json:"is_featured"`            // 是否为精选模板
	IsDeleted     *bool      `gorm:"column:is_deleted;type:boolean" json:"is_deleted"`
}

// TableName OnlineTemplate's table name
func (*OnlineTemplate) TableName() string {
	return TableNameOnlineTemplate
}
