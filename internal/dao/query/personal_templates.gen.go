// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"fp-browser/cmd/gen/fp-browser/internal/dao/model"
)

func newPersonalTemplate(db *gorm.DB, opts ...gen.DOOption) personalTemplate {
	_personalTemplate := personalTemplate{}

	_personalTemplate.personalTemplateDo.UseDB(db, opts...)
	_personalTemplate.personalTemplateDo.UseModel(&model.PersonalTemplate{})

	tableName := _personalTemplate.personalTemplateDo.TableName()
	_personalTemplate.ALL = field.NewAsterisk(tableName)
	_personalTemplate.ID = field.NewInt32(tableName, "id")
	_personalTemplate.Name = field.NewString(tableName, "name")
	_personalTemplate.Template = field.NewString(tableName, "template")
	_personalTemplate.CreatedAt = field.NewTime(tableName, "created_at")
	_personalTemplate.UpdatedAt = field.NewTime(tableName, "updated_at")
	_personalTemplate.Tags = field.NewString(tableName, "tags")
	_personalTemplate.UserID = field.NewInt32(tableName, "user_id")
	_personalTemplate.IsDeleted = field.NewBool(tableName, "is_deleted")

	_personalTemplate.fillFieldMap()

	return _personalTemplate
}

type personalTemplate struct {
	personalTemplateDo

	ALL       field.Asterisk
	ID        field.Int32
	Name      field.String // 模板名称
	Template  field.String // YAML配置文件内容，以字符串形式存储
	CreatedAt field.Time
	UpdatedAt field.Time
	Tags      field.String // 模板标签数组
	UserID    field.Int32  // 模板所属用户ID
	IsDeleted field.Bool

	fieldMap map[string]field.Expr
}

func (p personalTemplate) Table(newTableName string) *personalTemplate {
	p.personalTemplateDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p personalTemplate) As(alias string) *personalTemplate {
	p.personalTemplateDo.DO = *(p.personalTemplateDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *personalTemplate) updateTableName(table string) *personalTemplate {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt32(table, "id")
	p.Name = field.NewString(table, "name")
	p.Template = field.NewString(table, "template")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.Tags = field.NewString(table, "tags")
	p.UserID = field.NewInt32(table, "user_id")
	p.IsDeleted = field.NewBool(table, "is_deleted")

	p.fillFieldMap()

	return p
}

func (p *personalTemplate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *personalTemplate) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 8)
	p.fieldMap["id"] = p.ID
	p.fieldMap["name"] = p.Name
	p.fieldMap["template"] = p.Template
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["tags"] = p.Tags
	p.fieldMap["user_id"] = p.UserID
	p.fieldMap["is_deleted"] = p.IsDeleted
}

func (p personalTemplate) clone(db *gorm.DB) personalTemplate {
	p.personalTemplateDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p personalTemplate) replaceDB(db *gorm.DB) personalTemplate {
	p.personalTemplateDo.ReplaceDB(db)
	return p
}

type personalTemplateDo struct{ gen.DO }

type IPersonalTemplateDo interface {
	gen.SubQuery
	Debug() IPersonalTemplateDo
	WithContext(ctx context.Context) IPersonalTemplateDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPersonalTemplateDo
	WriteDB() IPersonalTemplateDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPersonalTemplateDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPersonalTemplateDo
	Not(conds ...gen.Condition) IPersonalTemplateDo
	Or(conds ...gen.Condition) IPersonalTemplateDo
	Select(conds ...field.Expr) IPersonalTemplateDo
	Where(conds ...gen.Condition) IPersonalTemplateDo
	Order(conds ...field.Expr) IPersonalTemplateDo
	Distinct(cols ...field.Expr) IPersonalTemplateDo
	Omit(cols ...field.Expr) IPersonalTemplateDo
	Join(table schema.Tabler, on ...field.Expr) IPersonalTemplateDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPersonalTemplateDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPersonalTemplateDo
	Group(cols ...field.Expr) IPersonalTemplateDo
	Having(conds ...gen.Condition) IPersonalTemplateDo
	Limit(limit int) IPersonalTemplateDo
	Offset(offset int) IPersonalTemplateDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPersonalTemplateDo
	Unscoped() IPersonalTemplateDo
	Create(values ...*model.PersonalTemplate) error
	CreateInBatches(values []*model.PersonalTemplate, batchSize int) error
	Save(values ...*model.PersonalTemplate) error
	First() (*model.PersonalTemplate, error)
	Take() (*model.PersonalTemplate, error)
	Last() (*model.PersonalTemplate, error)
	Find() ([]*model.PersonalTemplate, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PersonalTemplate, err error)
	FindInBatches(result *[]*model.PersonalTemplate, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.PersonalTemplate) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPersonalTemplateDo
	Assign(attrs ...field.AssignExpr) IPersonalTemplateDo
	Joins(fields ...field.RelationField) IPersonalTemplateDo
	Preload(fields ...field.RelationField) IPersonalTemplateDo
	FirstOrInit() (*model.PersonalTemplate, error)
	FirstOrCreate() (*model.PersonalTemplate, error)
	FindByPage(offset int, limit int) (result []*model.PersonalTemplate, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPersonalTemplateDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p personalTemplateDo) Debug() IPersonalTemplateDo {
	return p.withDO(p.DO.Debug())
}

func (p personalTemplateDo) WithContext(ctx context.Context) IPersonalTemplateDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p personalTemplateDo) ReadDB() IPersonalTemplateDo {
	return p.Clauses(dbresolver.Read)
}

func (p personalTemplateDo) WriteDB() IPersonalTemplateDo {
	return p.Clauses(dbresolver.Write)
}

func (p personalTemplateDo) Session(config *gorm.Session) IPersonalTemplateDo {
	return p.withDO(p.DO.Session(config))
}

func (p personalTemplateDo) Clauses(conds ...clause.Expression) IPersonalTemplateDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p personalTemplateDo) Returning(value interface{}, columns ...string) IPersonalTemplateDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p personalTemplateDo) Not(conds ...gen.Condition) IPersonalTemplateDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p personalTemplateDo) Or(conds ...gen.Condition) IPersonalTemplateDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p personalTemplateDo) Select(conds ...field.Expr) IPersonalTemplateDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p personalTemplateDo) Where(conds ...gen.Condition) IPersonalTemplateDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p personalTemplateDo) Order(conds ...field.Expr) IPersonalTemplateDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p personalTemplateDo) Distinct(cols ...field.Expr) IPersonalTemplateDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p personalTemplateDo) Omit(cols ...field.Expr) IPersonalTemplateDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p personalTemplateDo) Join(table schema.Tabler, on ...field.Expr) IPersonalTemplateDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p personalTemplateDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPersonalTemplateDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p personalTemplateDo) RightJoin(table schema.Tabler, on ...field.Expr) IPersonalTemplateDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p personalTemplateDo) Group(cols ...field.Expr) IPersonalTemplateDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p personalTemplateDo) Having(conds ...gen.Condition) IPersonalTemplateDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p personalTemplateDo) Limit(limit int) IPersonalTemplateDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p personalTemplateDo) Offset(offset int) IPersonalTemplateDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p personalTemplateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPersonalTemplateDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p personalTemplateDo) Unscoped() IPersonalTemplateDo {
	return p.withDO(p.DO.Unscoped())
}

func (p personalTemplateDo) Create(values ...*model.PersonalTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p personalTemplateDo) CreateInBatches(values []*model.PersonalTemplate, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p personalTemplateDo) Save(values ...*model.PersonalTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p personalTemplateDo) First() (*model.PersonalTemplate, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PersonalTemplate), nil
	}
}

func (p personalTemplateDo) Take() (*model.PersonalTemplate, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PersonalTemplate), nil
	}
}

func (p personalTemplateDo) Last() (*model.PersonalTemplate, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PersonalTemplate), nil
	}
}

func (p personalTemplateDo) Find() ([]*model.PersonalTemplate, error) {
	result, err := p.DO.Find()
	return result.([]*model.PersonalTemplate), err
}

func (p personalTemplateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PersonalTemplate, err error) {
	buf := make([]*model.PersonalTemplate, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p personalTemplateDo) FindInBatches(result *[]*model.PersonalTemplate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p personalTemplateDo) Attrs(attrs ...field.AssignExpr) IPersonalTemplateDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p personalTemplateDo) Assign(attrs ...field.AssignExpr) IPersonalTemplateDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p personalTemplateDo) Joins(fields ...field.RelationField) IPersonalTemplateDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p personalTemplateDo) Preload(fields ...field.RelationField) IPersonalTemplateDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p personalTemplateDo) FirstOrInit() (*model.PersonalTemplate, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PersonalTemplate), nil
	}
}

func (p personalTemplateDo) FirstOrCreate() (*model.PersonalTemplate, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PersonalTemplate), nil
	}
}

func (p personalTemplateDo) FindByPage(offset int, limit int) (result []*model.PersonalTemplate, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p personalTemplateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p personalTemplateDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p personalTemplateDo) Delete(models ...*model.PersonalTemplate) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *personalTemplateDo) withDO(do gen.Dao) *personalTemplateDo {
	p.DO = *do.(*gen.DO)
	return p
}
