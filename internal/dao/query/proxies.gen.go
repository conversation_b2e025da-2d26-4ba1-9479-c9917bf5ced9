// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"fp-browser/cmd/gen/fp-browser/internal/dao/model"
)

func newProxy(db *gorm.DB, opts ...gen.DOOption) proxy {
	_proxy := proxy{}

	_proxy.proxyDo.UseDB(db, opts...)
	_proxy.proxyDo.UseModel(&model.Proxy{})

	tableName := _proxy.proxyDo.TableName()
	_proxy.ALL = field.NewAsterisk(tableName)
	_proxy.ID = field.NewInt32(tableName, "id")
	_proxy.Name = field.NewString(tableName, "name")
	_proxy.IPAddress = field.NewInt32(tableName, "ip_address")
	_proxy.Port = field.NewInt16(tableName, "port")
	_proxy.SocksUsername = field.NewString(tableName, "socks_username")
	_proxy.SocksPassword = field.NewString(tableName, "socks_password")
	_proxy.TeamID = field.NewInt32(tableName, "team_id")
	_proxy.EnvironmentID = field.NewInt32(tableName, "environment_id")
	_proxy.NodeID = field.NewInt32(tableName, "node_id")
	_proxy.Region = field.NewString(tableName, "region")
	_proxy.TrafficLimit = field.NewInt64(tableName, "traffic_limit")
	_proxy.Unlimited = field.NewBool(tableName, "unlimited")
	_proxy.IsForwarding = field.NewBool(tableName, "is_forwarding")
	_proxy.ForwardID = field.NewInt32(tableName, "forward_id")
	_proxy.AutoRenew = field.NewBool(tableName, "auto_renew")
	_proxy.SpeedLimit = field.NewInt16(tableName, "speed_limit")
	_proxy.TuicUsername = field.NewString(tableName, "tuic_username")
	_proxy.ExpiresAt = field.NewTime(tableName, "expires_at")
	_proxy.CreatedAt = field.NewTime(tableName, "created_at")
	_proxy.UpdatedAt = field.NewTime(tableName, "updated_at")

	_proxy.fillFieldMap()

	return _proxy
}

type proxy struct {
	proxyDo

	ALL           field.Asterisk
	ID            field.Int32  // 代理 ID，自增主键
	Name          field.String // 代理名称
	IPAddress     field.Int32  // IPv4 地址，整数形式
	Port          field.Int16  // 端口号
	SocksUsername field.String // socks5 登录用户名
	SocksPassword field.String // socks5 登录密码
	TeamID        field.Int32  // 绑定的团队 ID
	EnvironmentID field.Int32  // 绑定的环境 ID
	NodeID        field.Int32  // 代理所属节点
	Region        field.String // 地区信息
	TrafficLimit  field.Int64  // 最大流量限制（单位：字节）
	Unlimited     field.Bool   // 是否为无限流量
	IsForwarding  field.Bool   // 是否启用转发代理
	ForwardID     field.Int32  // 转发配置 ID（Forward 表）
	AutoRenew     field.Bool   // 是否启用自动续费
	SpeedLimit    field.Int16  // 限速值，单位可能为 KB/s
	TuicUsername  field.String // TUIC 协议使用的 UUID 用户名
	ExpiresAt     field.Time   // 代理过期时间
	CreatedAt     field.Time   // 创建时间
	UpdatedAt     field.Time   // 最近更新时间

	fieldMap map[string]field.Expr
}

func (p proxy) Table(newTableName string) *proxy {
	p.proxyDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p proxy) As(alias string) *proxy {
	p.proxyDo.DO = *(p.proxyDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *proxy) updateTableName(table string) *proxy {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt32(table, "id")
	p.Name = field.NewString(table, "name")
	p.IPAddress = field.NewInt32(table, "ip_address")
	p.Port = field.NewInt16(table, "port")
	p.SocksUsername = field.NewString(table, "socks_username")
	p.SocksPassword = field.NewString(table, "socks_password")
	p.TeamID = field.NewInt32(table, "team_id")
	p.EnvironmentID = field.NewInt32(table, "environment_id")
	p.NodeID = field.NewInt32(table, "node_id")
	p.Region = field.NewString(table, "region")
	p.TrafficLimit = field.NewInt64(table, "traffic_limit")
	p.Unlimited = field.NewBool(table, "unlimited")
	p.IsForwarding = field.NewBool(table, "is_forwarding")
	p.ForwardID = field.NewInt32(table, "forward_id")
	p.AutoRenew = field.NewBool(table, "auto_renew")
	p.SpeedLimit = field.NewInt16(table, "speed_limit")
	p.TuicUsername = field.NewString(table, "tuic_username")
	p.ExpiresAt = field.NewTime(table, "expires_at")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")

	p.fillFieldMap()

	return p
}

func (p *proxy) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *proxy) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 20)
	p.fieldMap["id"] = p.ID
	p.fieldMap["name"] = p.Name
	p.fieldMap["ip_address"] = p.IPAddress
	p.fieldMap["port"] = p.Port
	p.fieldMap["socks_username"] = p.SocksUsername
	p.fieldMap["socks_password"] = p.SocksPassword
	p.fieldMap["team_id"] = p.TeamID
	p.fieldMap["environment_id"] = p.EnvironmentID
	p.fieldMap["node_id"] = p.NodeID
	p.fieldMap["region"] = p.Region
	p.fieldMap["traffic_limit"] = p.TrafficLimit
	p.fieldMap["unlimited"] = p.Unlimited
	p.fieldMap["is_forwarding"] = p.IsForwarding
	p.fieldMap["forward_id"] = p.ForwardID
	p.fieldMap["auto_renew"] = p.AutoRenew
	p.fieldMap["speed_limit"] = p.SpeedLimit
	p.fieldMap["tuic_username"] = p.TuicUsername
	p.fieldMap["expires_at"] = p.ExpiresAt
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
}

func (p proxy) clone(db *gorm.DB) proxy {
	p.proxyDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p proxy) replaceDB(db *gorm.DB) proxy {
	p.proxyDo.ReplaceDB(db)
	return p
}

type proxyDo struct{ gen.DO }

type IProxyDo interface {
	gen.SubQuery
	Debug() IProxyDo
	WithContext(ctx context.Context) IProxyDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IProxyDo
	WriteDB() IProxyDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IProxyDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IProxyDo
	Not(conds ...gen.Condition) IProxyDo
	Or(conds ...gen.Condition) IProxyDo
	Select(conds ...field.Expr) IProxyDo
	Where(conds ...gen.Condition) IProxyDo
	Order(conds ...field.Expr) IProxyDo
	Distinct(cols ...field.Expr) IProxyDo
	Omit(cols ...field.Expr) IProxyDo
	Join(table schema.Tabler, on ...field.Expr) IProxyDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IProxyDo
	RightJoin(table schema.Tabler, on ...field.Expr) IProxyDo
	Group(cols ...field.Expr) IProxyDo
	Having(conds ...gen.Condition) IProxyDo
	Limit(limit int) IProxyDo
	Offset(offset int) IProxyDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IProxyDo
	Unscoped() IProxyDo
	Create(values ...*model.Proxy) error
	CreateInBatches(values []*model.Proxy, batchSize int) error
	Save(values ...*model.Proxy) error
	First() (*model.Proxy, error)
	Take() (*model.Proxy, error)
	Last() (*model.Proxy, error)
	Find() ([]*model.Proxy, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Proxy, err error)
	FindInBatches(result *[]*model.Proxy, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Proxy) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IProxyDo
	Assign(attrs ...field.AssignExpr) IProxyDo
	Joins(fields ...field.RelationField) IProxyDo
	Preload(fields ...field.RelationField) IProxyDo
	FirstOrInit() (*model.Proxy, error)
	FirstOrCreate() (*model.Proxy, error)
	FindByPage(offset int, limit int) (result []*model.Proxy, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IProxyDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p proxyDo) Debug() IProxyDo {
	return p.withDO(p.DO.Debug())
}

func (p proxyDo) WithContext(ctx context.Context) IProxyDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p proxyDo) ReadDB() IProxyDo {
	return p.Clauses(dbresolver.Read)
}

func (p proxyDo) WriteDB() IProxyDo {
	return p.Clauses(dbresolver.Write)
}

func (p proxyDo) Session(config *gorm.Session) IProxyDo {
	return p.withDO(p.DO.Session(config))
}

func (p proxyDo) Clauses(conds ...clause.Expression) IProxyDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p proxyDo) Returning(value interface{}, columns ...string) IProxyDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p proxyDo) Not(conds ...gen.Condition) IProxyDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p proxyDo) Or(conds ...gen.Condition) IProxyDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p proxyDo) Select(conds ...field.Expr) IProxyDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p proxyDo) Where(conds ...gen.Condition) IProxyDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p proxyDo) Order(conds ...field.Expr) IProxyDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p proxyDo) Distinct(cols ...field.Expr) IProxyDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p proxyDo) Omit(cols ...field.Expr) IProxyDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p proxyDo) Join(table schema.Tabler, on ...field.Expr) IProxyDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p proxyDo) LeftJoin(table schema.Tabler, on ...field.Expr) IProxyDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p proxyDo) RightJoin(table schema.Tabler, on ...field.Expr) IProxyDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p proxyDo) Group(cols ...field.Expr) IProxyDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p proxyDo) Having(conds ...gen.Condition) IProxyDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p proxyDo) Limit(limit int) IProxyDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p proxyDo) Offset(offset int) IProxyDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p proxyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IProxyDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p proxyDo) Unscoped() IProxyDo {
	return p.withDO(p.DO.Unscoped())
}

func (p proxyDo) Create(values ...*model.Proxy) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p proxyDo) CreateInBatches(values []*model.Proxy, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p proxyDo) Save(values ...*model.Proxy) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p proxyDo) First() (*model.Proxy, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Proxy), nil
	}
}

func (p proxyDo) Take() (*model.Proxy, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Proxy), nil
	}
}

func (p proxyDo) Last() (*model.Proxy, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Proxy), nil
	}
}

func (p proxyDo) Find() ([]*model.Proxy, error) {
	result, err := p.DO.Find()
	return result.([]*model.Proxy), err
}

func (p proxyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Proxy, err error) {
	buf := make([]*model.Proxy, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p proxyDo) FindInBatches(result *[]*model.Proxy, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p proxyDo) Attrs(attrs ...field.AssignExpr) IProxyDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p proxyDo) Assign(attrs ...field.AssignExpr) IProxyDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p proxyDo) Joins(fields ...field.RelationField) IProxyDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p proxyDo) Preload(fields ...field.RelationField) IProxyDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p proxyDo) FirstOrInit() (*model.Proxy, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Proxy), nil
	}
}

func (p proxyDo) FirstOrCreate() (*model.Proxy, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Proxy), nil
	}
}

func (p proxyDo) FindByPage(offset int, limit int) (result []*model.Proxy, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p proxyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p proxyDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p proxyDo) Delete(models ...*model.Proxy) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *proxyDo) withDO(do gen.Dao) *proxyDo {
	p.DO = *do.(*gen.DO)
	return p
}
