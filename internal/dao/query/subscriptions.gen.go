// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"fp-browser/cmd/gen/fp-browser/internal/dao/model"
)

func newSubscription(db *gorm.DB, opts ...gen.DOOption) subscription {
	_subscription := subscription{}

	_subscription.subscriptionDo.UseDB(db, opts...)
	_subscription.subscriptionDo.UseModel(&model.Subscription{})

	tableName := _subscription.subscriptionDo.TableName()
	_subscription.ALL = field.NewAsterisk(tableName)
	_subscription.ID = field.NewInt32(tableName, "id")
	_subscription.Name = field.NewString(tableName, "name")
	_subscription.Storagesize = field.NewInt64(tableName, "storagesize")
	_subscription.Membercount = field.NewInt32(tableName, "membercount")
	_subscription.Price = field.NewFloat64(tableName, "price")
	_subscription.Currency = field.NewString(tableName, "currency")
	_subscription.CreatedAt = field.NewTime(tableName, "created_at")
	_subscription.UpdatedAt = field.NewTime(tableName, "updated_at")

	_subscription.fillFieldMap()

	return _subscription
}

type subscription struct {
	subscriptionDo

	ALL         field.Asterisk
	ID          field.Int32
	Name        field.String  // 本地化名称，使用 JSONB 存储
	Storagesize field.Int64   // 存储容量限制，单位为字节（Byte）
	Membercount field.Int32   // 成员数量限制
	Price       field.Float64 // 价格金额
	Currency    field.String  // 币种，ISO 4217 代码，如 CNY, USD, JPY
	CreatedAt   field.Time    // 创建时间
	UpdatedAt   field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (s subscription) Table(newTableName string) *subscription {
	s.subscriptionDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s subscription) As(alias string) *subscription {
	s.subscriptionDo.DO = *(s.subscriptionDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *subscription) updateTableName(table string) *subscription {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt32(table, "id")
	s.Name = field.NewString(table, "name")
	s.Storagesize = field.NewInt64(table, "storagesize")
	s.Membercount = field.NewInt32(table, "membercount")
	s.Price = field.NewFloat64(table, "price")
	s.Currency = field.NewString(table, "currency")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedAt = field.NewTime(table, "updated_at")

	s.fillFieldMap()

	return s
}

func (s *subscription) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *subscription) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 8)
	s.fieldMap["id"] = s.ID
	s.fieldMap["name"] = s.Name
	s.fieldMap["storagesize"] = s.Storagesize
	s.fieldMap["membercount"] = s.Membercount
	s.fieldMap["price"] = s.Price
	s.fieldMap["currency"] = s.Currency
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
}

func (s subscription) clone(db *gorm.DB) subscription {
	s.subscriptionDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s subscription) replaceDB(db *gorm.DB) subscription {
	s.subscriptionDo.ReplaceDB(db)
	return s
}

type subscriptionDo struct{ gen.DO }

type ISubscriptionDo interface {
	gen.SubQuery
	Debug() ISubscriptionDo
	WithContext(ctx context.Context) ISubscriptionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISubscriptionDo
	WriteDB() ISubscriptionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISubscriptionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISubscriptionDo
	Not(conds ...gen.Condition) ISubscriptionDo
	Or(conds ...gen.Condition) ISubscriptionDo
	Select(conds ...field.Expr) ISubscriptionDo
	Where(conds ...gen.Condition) ISubscriptionDo
	Order(conds ...field.Expr) ISubscriptionDo
	Distinct(cols ...field.Expr) ISubscriptionDo
	Omit(cols ...field.Expr) ISubscriptionDo
	Join(table schema.Tabler, on ...field.Expr) ISubscriptionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISubscriptionDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISubscriptionDo
	Group(cols ...field.Expr) ISubscriptionDo
	Having(conds ...gen.Condition) ISubscriptionDo
	Limit(limit int) ISubscriptionDo
	Offset(offset int) ISubscriptionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISubscriptionDo
	Unscoped() ISubscriptionDo
	Create(values ...*model.Subscription) error
	CreateInBatches(values []*model.Subscription, batchSize int) error
	Save(values ...*model.Subscription) error
	First() (*model.Subscription, error)
	Take() (*model.Subscription, error)
	Last() (*model.Subscription, error)
	Find() ([]*model.Subscription, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Subscription, err error)
	FindInBatches(result *[]*model.Subscription, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Subscription) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISubscriptionDo
	Assign(attrs ...field.AssignExpr) ISubscriptionDo
	Joins(fields ...field.RelationField) ISubscriptionDo
	Preload(fields ...field.RelationField) ISubscriptionDo
	FirstOrInit() (*model.Subscription, error)
	FirstOrCreate() (*model.Subscription, error)
	FindByPage(offset int, limit int) (result []*model.Subscription, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISubscriptionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s subscriptionDo) Debug() ISubscriptionDo {
	return s.withDO(s.DO.Debug())
}

func (s subscriptionDo) WithContext(ctx context.Context) ISubscriptionDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s subscriptionDo) ReadDB() ISubscriptionDo {
	return s.Clauses(dbresolver.Read)
}

func (s subscriptionDo) WriteDB() ISubscriptionDo {
	return s.Clauses(dbresolver.Write)
}

func (s subscriptionDo) Session(config *gorm.Session) ISubscriptionDo {
	return s.withDO(s.DO.Session(config))
}

func (s subscriptionDo) Clauses(conds ...clause.Expression) ISubscriptionDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s subscriptionDo) Returning(value interface{}, columns ...string) ISubscriptionDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s subscriptionDo) Not(conds ...gen.Condition) ISubscriptionDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s subscriptionDo) Or(conds ...gen.Condition) ISubscriptionDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s subscriptionDo) Select(conds ...field.Expr) ISubscriptionDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s subscriptionDo) Where(conds ...gen.Condition) ISubscriptionDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s subscriptionDo) Order(conds ...field.Expr) ISubscriptionDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s subscriptionDo) Distinct(cols ...field.Expr) ISubscriptionDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s subscriptionDo) Omit(cols ...field.Expr) ISubscriptionDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s subscriptionDo) Join(table schema.Tabler, on ...field.Expr) ISubscriptionDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s subscriptionDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISubscriptionDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s subscriptionDo) RightJoin(table schema.Tabler, on ...field.Expr) ISubscriptionDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s subscriptionDo) Group(cols ...field.Expr) ISubscriptionDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s subscriptionDo) Having(conds ...gen.Condition) ISubscriptionDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s subscriptionDo) Limit(limit int) ISubscriptionDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s subscriptionDo) Offset(offset int) ISubscriptionDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s subscriptionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISubscriptionDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s subscriptionDo) Unscoped() ISubscriptionDo {
	return s.withDO(s.DO.Unscoped())
}

func (s subscriptionDo) Create(values ...*model.Subscription) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s subscriptionDo) CreateInBatches(values []*model.Subscription, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s subscriptionDo) Save(values ...*model.Subscription) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s subscriptionDo) First() (*model.Subscription, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Subscription), nil
	}
}

func (s subscriptionDo) Take() (*model.Subscription, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Subscription), nil
	}
}

func (s subscriptionDo) Last() (*model.Subscription, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Subscription), nil
	}
}

func (s subscriptionDo) Find() ([]*model.Subscription, error) {
	result, err := s.DO.Find()
	return result.([]*model.Subscription), err
}

func (s subscriptionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Subscription, err error) {
	buf := make([]*model.Subscription, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s subscriptionDo) FindInBatches(result *[]*model.Subscription, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s subscriptionDo) Attrs(attrs ...field.AssignExpr) ISubscriptionDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s subscriptionDo) Assign(attrs ...field.AssignExpr) ISubscriptionDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s subscriptionDo) Joins(fields ...field.RelationField) ISubscriptionDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s subscriptionDo) Preload(fields ...field.RelationField) ISubscriptionDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s subscriptionDo) FirstOrInit() (*model.Subscription, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Subscription), nil
	}
}

func (s subscriptionDo) FirstOrCreate() (*model.Subscription, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Subscription), nil
	}
}

func (s subscriptionDo) FindByPage(offset int, limit int) (result []*model.Subscription, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s subscriptionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s subscriptionDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s subscriptionDo) Delete(models ...*model.Subscription) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *subscriptionDo) withDO(do gen.Dao) *subscriptionDo {
	s.DO = *do.(*gen.DO)
	return s
}
