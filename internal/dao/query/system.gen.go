// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"fp-browser/cmd/gen/fp-browser/internal/dao/model"
)

func newSystem(db *gorm.DB, opts ...gen.DOOption) system {
	_system := system{}

	_system.systemDo.UseDB(db, opts...)
	_system.systemDo.UseModel(&model.System{})

	tableName := _system.systemDo.TableName()
	_system.ALL = field.NewAsterisk(tableName)
	_system.Key = field.NewString(tableName, "key")
	_system.Value = field.NewString(tableName, "value")
	_system.ValueType = field.NewString(tableName, "value_type")
	_system.Description = field.NewString(tableName, "description")

	_system.fillFieldMap()

	return _system
}

type system struct {
	systemDo

	ALL         field.Asterisk
	Key         field.String // 配置项唯一键
	Value       field.String // 配置值，存为文本，业务层解析
	ValueType   field.String // 值类型（string, int, bool, json等）
	Description field.String // 配置项说明备注

	fieldMap map[string]field.Expr
}

func (s system) Table(newTableName string) *system {
	s.systemDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s system) As(alias string) *system {
	s.systemDo.DO = *(s.systemDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *system) updateTableName(table string) *system {
	s.ALL = field.NewAsterisk(table)
	s.Key = field.NewString(table, "key")
	s.Value = field.NewString(table, "value")
	s.ValueType = field.NewString(table, "value_type")
	s.Description = field.NewString(table, "description")

	s.fillFieldMap()

	return s
}

func (s *system) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *system) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 4)
	s.fieldMap["key"] = s.Key
	s.fieldMap["value"] = s.Value
	s.fieldMap["value_type"] = s.ValueType
	s.fieldMap["description"] = s.Description
}

func (s system) clone(db *gorm.DB) system {
	s.systemDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s system) replaceDB(db *gorm.DB) system {
	s.systemDo.ReplaceDB(db)
	return s
}

type systemDo struct{ gen.DO }

type ISystemDo interface {
	gen.SubQuery
	Debug() ISystemDo
	WithContext(ctx context.Context) ISystemDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISystemDo
	WriteDB() ISystemDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISystemDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISystemDo
	Not(conds ...gen.Condition) ISystemDo
	Or(conds ...gen.Condition) ISystemDo
	Select(conds ...field.Expr) ISystemDo
	Where(conds ...gen.Condition) ISystemDo
	Order(conds ...field.Expr) ISystemDo
	Distinct(cols ...field.Expr) ISystemDo
	Omit(cols ...field.Expr) ISystemDo
	Join(table schema.Tabler, on ...field.Expr) ISystemDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISystemDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISystemDo
	Group(cols ...field.Expr) ISystemDo
	Having(conds ...gen.Condition) ISystemDo
	Limit(limit int) ISystemDo
	Offset(offset int) ISystemDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISystemDo
	Unscoped() ISystemDo
	Create(values ...*model.System) error
	CreateInBatches(values []*model.System, batchSize int) error
	Save(values ...*model.System) error
	First() (*model.System, error)
	Take() (*model.System, error)
	Last() (*model.System, error)
	Find() ([]*model.System, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.System, err error)
	FindInBatches(result *[]*model.System, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.System) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISystemDo
	Assign(attrs ...field.AssignExpr) ISystemDo
	Joins(fields ...field.RelationField) ISystemDo
	Preload(fields ...field.RelationField) ISystemDo
	FirstOrInit() (*model.System, error)
	FirstOrCreate() (*model.System, error)
	FindByPage(offset int, limit int) (result []*model.System, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISystemDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s systemDo) Debug() ISystemDo {
	return s.withDO(s.DO.Debug())
}

func (s systemDo) WithContext(ctx context.Context) ISystemDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s systemDo) ReadDB() ISystemDo {
	return s.Clauses(dbresolver.Read)
}

func (s systemDo) WriteDB() ISystemDo {
	return s.Clauses(dbresolver.Write)
}

func (s systemDo) Session(config *gorm.Session) ISystemDo {
	return s.withDO(s.DO.Session(config))
}

func (s systemDo) Clauses(conds ...clause.Expression) ISystemDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s systemDo) Returning(value interface{}, columns ...string) ISystemDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s systemDo) Not(conds ...gen.Condition) ISystemDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s systemDo) Or(conds ...gen.Condition) ISystemDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s systemDo) Select(conds ...field.Expr) ISystemDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s systemDo) Where(conds ...gen.Condition) ISystemDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s systemDo) Order(conds ...field.Expr) ISystemDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s systemDo) Distinct(cols ...field.Expr) ISystemDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s systemDo) Omit(cols ...field.Expr) ISystemDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s systemDo) Join(table schema.Tabler, on ...field.Expr) ISystemDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s systemDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISystemDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s systemDo) RightJoin(table schema.Tabler, on ...field.Expr) ISystemDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s systemDo) Group(cols ...field.Expr) ISystemDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s systemDo) Having(conds ...gen.Condition) ISystemDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s systemDo) Limit(limit int) ISystemDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s systemDo) Offset(offset int) ISystemDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s systemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISystemDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s systemDo) Unscoped() ISystemDo {
	return s.withDO(s.DO.Unscoped())
}

func (s systemDo) Create(values ...*model.System) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s systemDo) CreateInBatches(values []*model.System, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s systemDo) Save(values ...*model.System) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s systemDo) First() (*model.System, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.System), nil
	}
}

func (s systemDo) Take() (*model.System, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.System), nil
	}
}

func (s systemDo) Last() (*model.System, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.System), nil
	}
}

func (s systemDo) Find() ([]*model.System, error) {
	result, err := s.DO.Find()
	return result.([]*model.System), err
}

func (s systemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.System, err error) {
	buf := make([]*model.System, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s systemDo) FindInBatches(result *[]*model.System, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s systemDo) Attrs(attrs ...field.AssignExpr) ISystemDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s systemDo) Assign(attrs ...field.AssignExpr) ISystemDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s systemDo) Joins(fields ...field.RelationField) ISystemDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s systemDo) Preload(fields ...field.RelationField) ISystemDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s systemDo) FirstOrInit() (*model.System, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.System), nil
	}
}

func (s systemDo) FirstOrCreate() (*model.System, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.System), nil
	}
}

func (s systemDo) FindByPage(offset int, limit int) (result []*model.System, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s systemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s systemDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s systemDo) Delete(models ...*model.System) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *systemDo) withDO(do gen.Dao) *systemDo {
	s.DO = *do.(*gen.DO)
	return s
}
