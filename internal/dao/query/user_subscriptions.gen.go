// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"fp-browser/cmd/gen/fp-browser/internal/dao/model"
)

func newUserSubscription(db *gorm.DB, opts ...gen.DOOption) userSubscription {
	_userSubscription := userSubscription{}

	_userSubscription.userSubscriptionDo.UseDB(db, opts...)
	_userSubscription.userSubscriptionDo.UseModel(&model.UserSubscription{})

	tableName := _userSubscription.userSubscriptionDo.TableName()
	_userSubscription.ALL = field.NewAsterisk(tableName)
	_userSubscription.ID = field.NewInt32(tableName, "id")
	_userSubscription.OrderID = field.NewInt32(tableName, "order_id")
	_userSubscription.TeamID = field.NewInt32(tableName, "team_id")
	_userSubscription.StartDate = field.NewTime(tableName, "start_date")
	_userSubscription.EndDate = field.NewTime(tableName, "end_date")
	_userSubscription.StorageSize = field.NewInt64(tableName, "storage_size")
	_userSubscription.MembersCount = field.NewInt16(tableName, "members_count")
	_userSubscription.TotalPrice = field.NewInt64(tableName, "total_price")
	_userSubscription.Status = field.NewInt16(tableName, "status")
	_userSubscription.CreatedAt = field.NewTime(tableName, "created_at")
	_userSubscription.UpdatedAt = field.NewTime(tableName, "updated_at")

	_userSubscription.fillFieldMap()

	return _userSubscription
}

type userSubscription struct {
	userSubscriptionDo

	ALL          field.Asterisk
	ID           field.Int32 // 订阅记录主键 ID
	OrderID      field.Int32 // 对应订单 ID
	TeamID       field.Int32 // 团队 ID
	StartDate    field.Time  // 订阅开始时间
	EndDate      field.Time  // 订阅结束时间
	StorageSize  field.Int64 // 容量限制
	MembersCount field.Int16 // 订阅成员数量限制
	TotalPrice   field.Int64 // 购买总价（单位分）
	Status       field.Int16 // 订阅状态（1: Active, 2: Expired, 3: Cancelled, 4: Pending）
	CreatedAt    field.Time  // 创建时间
	UpdatedAt    field.Time  // 更新时间

	fieldMap map[string]field.Expr
}

func (u userSubscription) Table(newTableName string) *userSubscription {
	u.userSubscriptionDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userSubscription) As(alias string) *userSubscription {
	u.userSubscriptionDo.DO = *(u.userSubscriptionDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userSubscription) updateTableName(table string) *userSubscription {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewInt32(table, "id")
	u.OrderID = field.NewInt32(table, "order_id")
	u.TeamID = field.NewInt32(table, "team_id")
	u.StartDate = field.NewTime(table, "start_date")
	u.EndDate = field.NewTime(table, "end_date")
	u.StorageSize = field.NewInt64(table, "storage_size")
	u.MembersCount = field.NewInt16(table, "members_count")
	u.TotalPrice = field.NewInt64(table, "total_price")
	u.Status = field.NewInt16(table, "status")
	u.CreatedAt = field.NewTime(table, "created_at")
	u.UpdatedAt = field.NewTime(table, "updated_at")

	u.fillFieldMap()

	return u
}

func (u *userSubscription) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userSubscription) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 11)
	u.fieldMap["id"] = u.ID
	u.fieldMap["order_id"] = u.OrderID
	u.fieldMap["team_id"] = u.TeamID
	u.fieldMap["start_date"] = u.StartDate
	u.fieldMap["end_date"] = u.EndDate
	u.fieldMap["storage_size"] = u.StorageSize
	u.fieldMap["members_count"] = u.MembersCount
	u.fieldMap["total_price"] = u.TotalPrice
	u.fieldMap["status"] = u.Status
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
}

func (u userSubscription) clone(db *gorm.DB) userSubscription {
	u.userSubscriptionDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userSubscription) replaceDB(db *gorm.DB) userSubscription {
	u.userSubscriptionDo.ReplaceDB(db)
	return u
}

type userSubscriptionDo struct{ gen.DO }

type IUserSubscriptionDo interface {
	gen.SubQuery
	Debug() IUserSubscriptionDo
	WithContext(ctx context.Context) IUserSubscriptionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IUserSubscriptionDo
	WriteDB() IUserSubscriptionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IUserSubscriptionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IUserSubscriptionDo
	Not(conds ...gen.Condition) IUserSubscriptionDo
	Or(conds ...gen.Condition) IUserSubscriptionDo
	Select(conds ...field.Expr) IUserSubscriptionDo
	Where(conds ...gen.Condition) IUserSubscriptionDo
	Order(conds ...field.Expr) IUserSubscriptionDo
	Distinct(cols ...field.Expr) IUserSubscriptionDo
	Omit(cols ...field.Expr) IUserSubscriptionDo
	Join(table schema.Tabler, on ...field.Expr) IUserSubscriptionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IUserSubscriptionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IUserSubscriptionDo
	Group(cols ...field.Expr) IUserSubscriptionDo
	Having(conds ...gen.Condition) IUserSubscriptionDo
	Limit(limit int) IUserSubscriptionDo
	Offset(offset int) IUserSubscriptionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IUserSubscriptionDo
	Unscoped() IUserSubscriptionDo
	Create(values ...*model.UserSubscription) error
	CreateInBatches(values []*model.UserSubscription, batchSize int) error
	Save(values ...*model.UserSubscription) error
	First() (*model.UserSubscription, error)
	Take() (*model.UserSubscription, error)
	Last() (*model.UserSubscription, error)
	Find() ([]*model.UserSubscription, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserSubscription, err error)
	FindInBatches(result *[]*model.UserSubscription, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.UserSubscription) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IUserSubscriptionDo
	Assign(attrs ...field.AssignExpr) IUserSubscriptionDo
	Joins(fields ...field.RelationField) IUserSubscriptionDo
	Preload(fields ...field.RelationField) IUserSubscriptionDo
	FirstOrInit() (*model.UserSubscription, error)
	FirstOrCreate() (*model.UserSubscription, error)
	FindByPage(offset int, limit int) (result []*model.UserSubscription, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IUserSubscriptionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (u userSubscriptionDo) Debug() IUserSubscriptionDo {
	return u.withDO(u.DO.Debug())
}

func (u userSubscriptionDo) WithContext(ctx context.Context) IUserSubscriptionDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userSubscriptionDo) ReadDB() IUserSubscriptionDo {
	return u.Clauses(dbresolver.Read)
}

func (u userSubscriptionDo) WriteDB() IUserSubscriptionDo {
	return u.Clauses(dbresolver.Write)
}

func (u userSubscriptionDo) Session(config *gorm.Session) IUserSubscriptionDo {
	return u.withDO(u.DO.Session(config))
}

func (u userSubscriptionDo) Clauses(conds ...clause.Expression) IUserSubscriptionDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userSubscriptionDo) Returning(value interface{}, columns ...string) IUserSubscriptionDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userSubscriptionDo) Not(conds ...gen.Condition) IUserSubscriptionDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userSubscriptionDo) Or(conds ...gen.Condition) IUserSubscriptionDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userSubscriptionDo) Select(conds ...field.Expr) IUserSubscriptionDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userSubscriptionDo) Where(conds ...gen.Condition) IUserSubscriptionDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userSubscriptionDo) Order(conds ...field.Expr) IUserSubscriptionDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userSubscriptionDo) Distinct(cols ...field.Expr) IUserSubscriptionDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userSubscriptionDo) Omit(cols ...field.Expr) IUserSubscriptionDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userSubscriptionDo) Join(table schema.Tabler, on ...field.Expr) IUserSubscriptionDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userSubscriptionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IUserSubscriptionDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userSubscriptionDo) RightJoin(table schema.Tabler, on ...field.Expr) IUserSubscriptionDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userSubscriptionDo) Group(cols ...field.Expr) IUserSubscriptionDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userSubscriptionDo) Having(conds ...gen.Condition) IUserSubscriptionDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userSubscriptionDo) Limit(limit int) IUserSubscriptionDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userSubscriptionDo) Offset(offset int) IUserSubscriptionDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userSubscriptionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IUserSubscriptionDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userSubscriptionDo) Unscoped() IUserSubscriptionDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userSubscriptionDo) Create(values ...*model.UserSubscription) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userSubscriptionDo) CreateInBatches(values []*model.UserSubscription, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userSubscriptionDo) Save(values ...*model.UserSubscription) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userSubscriptionDo) First() (*model.UserSubscription, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserSubscription), nil
	}
}

func (u userSubscriptionDo) Take() (*model.UserSubscription, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserSubscription), nil
	}
}

func (u userSubscriptionDo) Last() (*model.UserSubscription, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserSubscription), nil
	}
}

func (u userSubscriptionDo) Find() ([]*model.UserSubscription, error) {
	result, err := u.DO.Find()
	return result.([]*model.UserSubscription), err
}

func (u userSubscriptionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserSubscription, err error) {
	buf := make([]*model.UserSubscription, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userSubscriptionDo) FindInBatches(result *[]*model.UserSubscription, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userSubscriptionDo) Attrs(attrs ...field.AssignExpr) IUserSubscriptionDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userSubscriptionDo) Assign(attrs ...field.AssignExpr) IUserSubscriptionDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userSubscriptionDo) Joins(fields ...field.RelationField) IUserSubscriptionDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userSubscriptionDo) Preload(fields ...field.RelationField) IUserSubscriptionDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userSubscriptionDo) FirstOrInit() (*model.UserSubscription, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserSubscription), nil
	}
}

func (u userSubscriptionDo) FirstOrCreate() (*model.UserSubscription, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserSubscription), nil
	}
}

func (u userSubscriptionDo) FindByPage(offset int, limit int) (result []*model.UserSubscription, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userSubscriptionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userSubscriptionDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userSubscriptionDo) Delete(models ...*model.UserSubscription) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userSubscriptionDo) withDO(do gen.Dao) *userSubscriptionDo {
	u.DO = *do.(*gen.DO)
	return u
}
