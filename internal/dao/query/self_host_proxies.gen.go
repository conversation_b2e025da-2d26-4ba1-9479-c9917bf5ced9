// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"fp-browser/cmd/gen/internal/dao/model"
)

func newSelfHostProxy(db *gorm.DB, opts ...gen.DOOption) selfHostProxy {
	_selfHostProxy := selfHostProxy{}

	_selfHostProxy.selfHostProxyDo.UseDB(db, opts...)
	_selfHostProxy.selfHostProxyDo.UseModel(&model.SelfHostProxy{})

	tableName := _selfHostProxy.selfHostProxyDo.TableName()
	_selfHostProxy.ALL = field.NewAsterisk(tableName)
	_selfHostProxy.ID = field.NewInt32(tableName, "id")
	_selfHostProxy.Name = field.NewString(tableName, "name")
	_selfHostProxy.Type = field.NewInt16(tableName, "type")
	_selfHostProxy.Host = field.NewString(tableName, "host")
	_selfHostProxy.Port = field.NewInt16(tableName, "port")
	_selfHostProxy.Username = field.NewString(tableName, "username")
	_selfHostProxy.Password = field.NewString(tableName, "password")
	_selfHostProxy.TeamID = field.NewInt32(tableName, "team_id")
	_selfHostProxy.EnvironmentID = field.NewInt32(tableName, "environment_id")

	_selfHostProxy.fillFieldMap()

	return _selfHostProxy
}

type selfHostProxy struct {
	selfHostProxyDo

	ALL           field.Asterisk
	ID            field.Int32  // 自增主键 ID
	Name          field.String // 代理名称，用户自定义标识
	Type          field.Int16  // 代理协议类型（1 HTTP, 2 HTTPS, 3 SOCKS5）
	Host          field.String // 主机地址，可为 IP 或域名
	Port          field.Int16  // 代理服务端口
	Username      field.String // 可选的代理用户名
	Password      field.String // 可选的代理密码
	TeamID        field.Int32  // 代理所属团队 ID
	EnvironmentID field.Int32  // 代理绑定的环境 ID

	fieldMap map[string]field.Expr
}

func (s selfHostProxy) Table(newTableName string) *selfHostProxy {
	s.selfHostProxyDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s selfHostProxy) As(alias string) *selfHostProxy {
	s.selfHostProxyDo.DO = *(s.selfHostProxyDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *selfHostProxy) updateTableName(table string) *selfHostProxy {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt32(table, "id")
	s.Name = field.NewString(table, "name")
	s.Type = field.NewInt16(table, "type")
	s.Host = field.NewString(table, "host")
	s.Port = field.NewInt16(table, "port")
	s.Username = field.NewString(table, "username")
	s.Password = field.NewString(table, "password")
	s.TeamID = field.NewInt32(table, "team_id")
	s.EnvironmentID = field.NewInt32(table, "environment_id")

	s.fillFieldMap()

	return s
}

func (s *selfHostProxy) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *selfHostProxy) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 9)
	s.fieldMap["id"] = s.ID
	s.fieldMap["name"] = s.Name
	s.fieldMap["type"] = s.Type
	s.fieldMap["host"] = s.Host
	s.fieldMap["port"] = s.Port
	s.fieldMap["username"] = s.Username
	s.fieldMap["password"] = s.Password
	s.fieldMap["team_id"] = s.TeamID
	s.fieldMap["environment_id"] = s.EnvironmentID
}

func (s selfHostProxy) clone(db *gorm.DB) selfHostProxy {
	s.selfHostProxyDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s selfHostProxy) replaceDB(db *gorm.DB) selfHostProxy {
	s.selfHostProxyDo.ReplaceDB(db)
	return s
}

type selfHostProxyDo struct{ gen.DO }

type ISelfHostProxyDo interface {
	gen.SubQuery
	Debug() ISelfHostProxyDo
	WithContext(ctx context.Context) ISelfHostProxyDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISelfHostProxyDo
	WriteDB() ISelfHostProxyDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISelfHostProxyDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISelfHostProxyDo
	Not(conds ...gen.Condition) ISelfHostProxyDo
	Or(conds ...gen.Condition) ISelfHostProxyDo
	Select(conds ...field.Expr) ISelfHostProxyDo
	Where(conds ...gen.Condition) ISelfHostProxyDo
	Order(conds ...field.Expr) ISelfHostProxyDo
	Distinct(cols ...field.Expr) ISelfHostProxyDo
	Omit(cols ...field.Expr) ISelfHostProxyDo
	Join(table schema.Tabler, on ...field.Expr) ISelfHostProxyDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISelfHostProxyDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISelfHostProxyDo
	Group(cols ...field.Expr) ISelfHostProxyDo
	Having(conds ...gen.Condition) ISelfHostProxyDo
	Limit(limit int) ISelfHostProxyDo
	Offset(offset int) ISelfHostProxyDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISelfHostProxyDo
	Unscoped() ISelfHostProxyDo
	Create(values ...*model.SelfHostProxy) error
	CreateInBatches(values []*model.SelfHostProxy, batchSize int) error
	Save(values ...*model.SelfHostProxy) error
	First() (*model.SelfHostProxy, error)
	Take() (*model.SelfHostProxy, error)
	Last() (*model.SelfHostProxy, error)
	Find() ([]*model.SelfHostProxy, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SelfHostProxy, err error)
	FindInBatches(result *[]*model.SelfHostProxy, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.SelfHostProxy) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISelfHostProxyDo
	Assign(attrs ...field.AssignExpr) ISelfHostProxyDo
	Joins(fields ...field.RelationField) ISelfHostProxyDo
	Preload(fields ...field.RelationField) ISelfHostProxyDo
	FirstOrInit() (*model.SelfHostProxy, error)
	FirstOrCreate() (*model.SelfHostProxy, error)
	FindByPage(offset int, limit int) (result []*model.SelfHostProxy, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISelfHostProxyDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s selfHostProxyDo) Debug() ISelfHostProxyDo {
	return s.withDO(s.DO.Debug())
}

func (s selfHostProxyDo) WithContext(ctx context.Context) ISelfHostProxyDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s selfHostProxyDo) ReadDB() ISelfHostProxyDo {
	return s.Clauses(dbresolver.Read)
}

func (s selfHostProxyDo) WriteDB() ISelfHostProxyDo {
	return s.Clauses(dbresolver.Write)
}

func (s selfHostProxyDo) Session(config *gorm.Session) ISelfHostProxyDo {
	return s.withDO(s.DO.Session(config))
}

func (s selfHostProxyDo) Clauses(conds ...clause.Expression) ISelfHostProxyDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s selfHostProxyDo) Returning(value interface{}, columns ...string) ISelfHostProxyDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s selfHostProxyDo) Not(conds ...gen.Condition) ISelfHostProxyDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s selfHostProxyDo) Or(conds ...gen.Condition) ISelfHostProxyDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s selfHostProxyDo) Select(conds ...field.Expr) ISelfHostProxyDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s selfHostProxyDo) Where(conds ...gen.Condition) ISelfHostProxyDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s selfHostProxyDo) Order(conds ...field.Expr) ISelfHostProxyDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s selfHostProxyDo) Distinct(cols ...field.Expr) ISelfHostProxyDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s selfHostProxyDo) Omit(cols ...field.Expr) ISelfHostProxyDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s selfHostProxyDo) Join(table schema.Tabler, on ...field.Expr) ISelfHostProxyDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s selfHostProxyDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISelfHostProxyDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s selfHostProxyDo) RightJoin(table schema.Tabler, on ...field.Expr) ISelfHostProxyDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s selfHostProxyDo) Group(cols ...field.Expr) ISelfHostProxyDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s selfHostProxyDo) Having(conds ...gen.Condition) ISelfHostProxyDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s selfHostProxyDo) Limit(limit int) ISelfHostProxyDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s selfHostProxyDo) Offset(offset int) ISelfHostProxyDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s selfHostProxyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISelfHostProxyDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s selfHostProxyDo) Unscoped() ISelfHostProxyDo {
	return s.withDO(s.DO.Unscoped())
}

func (s selfHostProxyDo) Create(values ...*model.SelfHostProxy) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s selfHostProxyDo) CreateInBatches(values []*model.SelfHostProxy, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s selfHostProxyDo) Save(values ...*model.SelfHostProxy) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s selfHostProxyDo) First() (*model.SelfHostProxy, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SelfHostProxy), nil
	}
}

func (s selfHostProxyDo) Take() (*model.SelfHostProxy, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SelfHostProxy), nil
	}
}

func (s selfHostProxyDo) Last() (*model.SelfHostProxy, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SelfHostProxy), nil
	}
}

func (s selfHostProxyDo) Find() ([]*model.SelfHostProxy, error) {
	result, err := s.DO.Find()
	return result.([]*model.SelfHostProxy), err
}

func (s selfHostProxyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SelfHostProxy, err error) {
	buf := make([]*model.SelfHostProxy, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s selfHostProxyDo) FindInBatches(result *[]*model.SelfHostProxy, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s selfHostProxyDo) Attrs(attrs ...field.AssignExpr) ISelfHostProxyDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s selfHostProxyDo) Assign(attrs ...field.AssignExpr) ISelfHostProxyDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s selfHostProxyDo) Joins(fields ...field.RelationField) ISelfHostProxyDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s selfHostProxyDo) Preload(fields ...field.RelationField) ISelfHostProxyDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s selfHostProxyDo) FirstOrInit() (*model.SelfHostProxy, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SelfHostProxy), nil
	}
}

func (s selfHostProxyDo) FirstOrCreate() (*model.SelfHostProxy, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SelfHostProxy), nil
	}
}

func (s selfHostProxyDo) FindByPage(offset int, limit int) (result []*model.SelfHostProxy, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s selfHostProxyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s selfHostProxyDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s selfHostProxyDo) Delete(models ...*model.SelfHostProxy) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *selfHostProxyDo) withDO(do gen.Dao) *selfHostProxyDo {
	s.DO = *do.(*gen.DO)
	return s
}
