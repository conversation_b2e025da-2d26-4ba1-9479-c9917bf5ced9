package repository

import (
	"errors"
	"strings"

	"gorm.io/gorm"

	"fp-browser/internal/dao/model"
)

// PersonalTemplateRepository 个人模板Repository接口
type PersonalTemplateRepository interface {
	Create(template *model.PersonalTemplate) error
	Update(template *model.PersonalTemplate) error
	Delete(id int32, userID int32) error
	GetByID(id int32, userID int32) (*model.PersonalTemplate, error)
	GetList(userID int32, offset, limit int, name string, tags []string) ([]*model.PersonalTemplate, int64, error)
	CheckNameExists(name string, userID int32, excludeID ...int32) (bool, error)
	GetByUserAndName(userID int32, name string) (*model.PersonalTemplate, error)
}

type personalTemplateRepository struct {
	repository *Repository
	db         *gorm.DB
}

// NewPersonalTemplateRepository 创建个人模板Repository实例
func NewPersonalTemplateRepository(repository *Repository, db *gorm.DB) PersonalTemplateRepository {
	return &personalTemplateRepository{
		repository: repository,
		db:         db,
	}
}

// Create 创建个人模板
func (r *personalTemplateRepository) Create(template *model.PersonalTemplate) error {
	if template == nil {
		return errors.New("template cannot be nil")
	}

	return r.db.Create(template).Error
}

// Update 更新个人模板
func (r *personalTemplateRepository) Update(template *model.PersonalTemplate) error {
	if template == nil {
		return errors.New("template cannot be nil")
	}

	result := r.db.Model(template).Where("id = ? AND user_id = ? AND (is_deleted IS NULL OR is_deleted = false)", template.ID, template.UserID).Updates(template)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("template not found or not authorized")
	}
	return nil
}

// Delete 删除个人模板（软删除）
func (r *personalTemplateRepository) Delete(id int32, userID int32) error {
	result := r.db.Model(&model.PersonalTemplate{}).
		Where("id = ? AND user_id = ? AND (is_deleted IS NULL OR is_deleted = false)", id, userID).
		Update("is_deleted", true)

	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("template not found or not authorized")
	}
	return nil
}

// GetByID 根据ID获取个人模板
func (r *personalTemplateRepository) GetByID(id int32, userID int32) (*model.PersonalTemplate, error) {
	var template model.PersonalTemplate
	err := r.db.Where("id = ? AND user_id = ? AND (is_deleted IS NULL OR is_deleted = false)", id, userID).First(&template).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("template not found")
		}
		return nil, err
	}
	return &template, nil
}

// GetList 获取个人模板列表
func (r *personalTemplateRepository) GetList(userID int32, offset, limit int, name string, tags []string) ([]*model.PersonalTemplate, int64, error) {
	var templates []*model.PersonalTemplate
	var total int64

	query := r.db.Model(&model.PersonalTemplate{}).Where("user_id = ? AND (is_deleted IS NULL OR is_deleted = false)", userID)

	// 添加名称筛选
	if name != "" {
		query = query.Where("name ILIKE ?", "%"+name+"%")
	}

	// 添加标签筛选
	if len(tags) > 0 {
		// 构建标签查询条件
		var tagConditions []string
		var tagArgs []interface{}
		for _, tag := range tags {
			tagConditions = append(tagConditions, "tags && ARRAY[?]")
			tagArgs = append(tagArgs, tag)
		}
		if len(tagConditions) > 0 {
			tagQuery := strings.Join(tagConditions, " AND ")
			query = query.Where(tagQuery, tagArgs...)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&templates).Error
	if err != nil {
		return nil, 0, err
	}

	return templates, total, nil
}

// CheckNameExists 检查名称是否已存在
func (r *personalTemplateRepository) CheckNameExists(name string, userID int32, excludeID ...int32) (bool, error) {
	var count int64
	query := r.db.Model(&model.PersonalTemplate{}).
		Where("name = ? AND user_id = ? AND (is_deleted IS NULL OR is_deleted = false)", name, userID)

	// 如果提供了排除ID，则排除该ID
	if len(excludeID) > 0 && excludeID[0] > 0 {
		query = query.Where("id != ?", excludeID[0])
	}

	err := query.Count(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// GetByUserAndName 根据用户ID和名称获取模板
func (r *personalTemplateRepository) GetByUserAndName(userID int32, name string) (*model.PersonalTemplate, error) {
	var template model.PersonalTemplate
	err := r.db.Where("user_id = ? AND name = ? AND (is_deleted IS NULL OR is_deleted = false)", userID, name).First(&template).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("template not found")
		}
		return nil, err
	}
	return &template, nil
}
