package repository

import (
	"errors"
	"fmt"
	"strings"

	"gorm.io/gorm"

	"fp-browser/internal/dao/model"
)

// OnlineTemplateRepository 在线模板Repository接口
type OnlineTemplateRepository interface {
	Create(template *model.OnlineTemplate) error
	Update(template *model.OnlineTemplate) error
	Delete(id int32, userID int32) error
	GetByID(id int32) (*model.OnlineTemplate, error)
	GetByIDForUser(id int32, userID int32) (*model.OnlineTemplate, error)
	GetList(offset, limit int, filters OnlineTemplateFilters) ([]*model.OnlineTemplate, int64, error)
	CheckNameExists(name string, createdBy int32, excludeID ...int32) (bool, error)
	IncrementDownloadCount(id int32) error
	GetFeaturedTemplates(limit int) ([]*model.OnlineTemplate, error)
	GetByCreatorAndName(createdBy int32, name string) (*model.OnlineTemplate, error)
}

// OnlineTemplateFilters 在线模板查询过滤器
type OnlineTemplateFilters struct {
	Name       string
	Tags       []string
	CreatedBy  int32
	IsPublic   *bool
	IsFeatured *bool
	SortBy     string // created_at, download_count
	Order      string // asc, desc
}

type onlineTemplateRepository struct {
	repository *Repository
	db         *gorm.DB
}

// NewOnlineTemplateRepository 创建在线模板Repository实例
func NewOnlineTemplateRepository(repository *Repository, db *gorm.DB) OnlineTemplateRepository {
	return &onlineTemplateRepository{
		repository: repository,
		db:         db,
	}
}

// Create 创建在线模板
func (r *onlineTemplateRepository) Create(template *model.OnlineTemplate) error {
	if template == nil {
		return errors.New("template cannot be nil")
	}

	return r.db.Create(template).Error
}

// Update 更新在线模板
func (r *onlineTemplateRepository) Update(template *model.OnlineTemplate) error {
	if template == nil {
		return errors.New("template cannot be nil")
	}

	result := r.db.Model(template).Where("id = ? AND created_by = ? AND (is_deleted IS NULL OR is_deleted = false)", template.ID, template.CreatedBy).Updates(template)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("template not found or not authorized")
	}
	return nil
}

// Delete 删除在线模板（软删除）
func (r *onlineTemplateRepository) Delete(id int32, userID int32) error {
	result := r.db.Model(&model.OnlineTemplate{}).
		Where("id = ? AND created_by = ? AND (is_deleted IS NULL OR is_deleted = false)", id, userID).
		Update("is_deleted", true)

	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("template not found or not authorized")
	}
	return nil
}

// GetByID 根据ID获取在线模板（不验证权限，用于公开访问）
func (r *onlineTemplateRepository) GetByID(id int32) (*model.OnlineTemplate, error) {
	var template model.OnlineTemplate
	err := r.db.Where("id = ? AND (is_deleted IS NULL OR is_deleted = false)", id).First(&template).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("template not found")
		}
		return nil, err
	}
	return &template, nil
}

// GetByIDForUser 根据ID获取在线模板（验证创建者权限）
func (r *onlineTemplateRepository) GetByIDForUser(id int32, userID int32) (*model.OnlineTemplate, error) {
	var template model.OnlineTemplate
	err := r.db.Where("id = ? AND created_by = ? AND (is_deleted IS NULL OR is_deleted = false)", id, userID).First(&template).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("template not found")
		}
		return nil, err
	}
	return &template, nil
}

// GetList 获取在线模板列表
func (r *onlineTemplateRepository) GetList(offset, limit int, filters OnlineTemplateFilters) ([]*model.OnlineTemplate, int64, error) {
	var templates []*model.OnlineTemplate
	var total int64

	query := r.db.Model(&model.OnlineTemplate{}).Where("is_deleted IS NULL OR is_deleted = false")

	// 添加名称筛选
	if filters.Name != "" {
		query = query.Where("name ILIKE ?", "%"+filters.Name+"%")
	}

	// 添加创建者筛选
	if filters.CreatedBy > 0 {
		query = query.Where("created_by = ?", filters.CreatedBy)
	}

	// 添加公开性筛选
	if filters.IsPublic != nil {
		query = query.Where("is_public = ?", *filters.IsPublic)
	}

	// 添加精选筛选
	if filters.IsFeatured != nil {
		query = query.Where("is_featured = ?", *filters.IsFeatured)
	}

	// 添加标签筛选
	if len(filters.Tags) > 0 {
		// 构建标签查询条件
		var tagConditions []string
		var tagArgs []interface{}
		for _, tag := range filters.Tags {
			tagConditions = append(tagConditions, "tags && ARRAY[?]")
			tagArgs = append(tagArgs, tag)
		}
		if len(tagConditions) > 0 {
			tagQuery := strings.Join(tagConditions, " AND ")
			query = query.Where(tagQuery, tagArgs...)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 添加排序
	orderBy := "created_at DESC" // 默认按创建时间降序
	if filters.SortBy != "" {
		switch filters.SortBy {
		case "created_at", "download_count":
			order := "DESC"
			if filters.Order == "asc" {
				order = "ASC"
			}
			orderBy = fmt.Sprintf("%s %s", filters.SortBy, order)
		}
	}

	// 获取分页数据
	err := query.Order(orderBy).Offset(offset).Limit(limit).Find(&templates).Error
	if err != nil {
		return nil, 0, err
	}

	return templates, total, nil
}

// CheckNameExists 检查名称是否已存在
func (r *onlineTemplateRepository) CheckNameExists(name string, createdBy int32, excludeID ...int32) (bool, error) {
	var count int64
	query := r.db.Model(&model.OnlineTemplate{}).
		Where("name = ? AND created_by = ? AND (is_deleted IS NULL OR is_deleted = false)", name, createdBy)

	// 如果提供了排除ID，则排除该ID
	if len(excludeID) > 0 && excludeID[0] > 0 {
		query = query.Where("id != ?", excludeID[0])
	}

	err := query.Count(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// IncrementDownloadCount 增加下载计数
func (r *onlineTemplateRepository) IncrementDownloadCount(id int32) error {
	result := r.db.Model(&model.OnlineTemplate{}).
		Where("id = ? AND (is_deleted IS NULL OR is_deleted = false)", id).
		Update("download_count", gorm.Expr("COALESCE(download_count, 0) + 1"))

	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("template not found")
	}
	return nil
}

// GetFeaturedTemplates 获取精选模板
func (r *onlineTemplateRepository) GetFeaturedTemplates(limit int) ([]*model.OnlineTemplate, error) {
	var templates []*model.OnlineTemplate
	err := r.db.Where("is_featured = true AND is_public = true AND (is_deleted IS NULL OR is_deleted = false)").
		Order("download_count DESC, created_at DESC").
		Limit(limit).
		Find(&templates).Error

	if err != nil {
		return nil, err
	}
	return templates, nil
}

// GetByCreatorAndName 根据创建者ID和名称获取模板
func (r *onlineTemplateRepository) GetByCreatorAndName(createdBy int32, name string) (*model.OnlineTemplate, error) {
	var template model.OnlineTemplate
	err := r.db.Where("created_by = ? AND name = ? AND (is_deleted IS NULL OR is_deleted = false)", createdBy, name).First(&template).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("template not found")
		}
		return nil, err
	}
	return &template, nil
}
