package user

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"fp-browser/api/v1/user"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
)

// OnlineTemplateService 在线模板服务接口
type OnlineTemplateService interface {
	CreateOnlineTemplate(userID int32, req *user.CreateOnlineTemplateRequest) (*user.CreateOnlineTemplateResponse, error)
	UpdateOnlineTemplate(userID int32, req *user.UpdateOnlineTemplateRequest) (*user.UpdateOnlineTemplateResponse, error)
	DeleteOnlineTemplate(userID int32, req *user.DeleteOnlineTemplateRequest) (*user.DeleteOnlineTemplateResponse, error)
	GetOnlineTemplate(req *user.GetOnlineTemplateRequest) (*user.GetOnlineTemplateResponse, error)
	GetOnlineTemplateList(req *user.GetOnlineTemplateListRequest) (*user.GetOnlineTemplateListResponse, error)
	DownloadOnlineTemplate(userID int32, req *user.DownloadOnlineTemplateRequest) (*user.DownloadOnlineTemplateResponse, error)
}

type onlineTemplateService struct {
	onlineTemplateRepo   repository.OnlineTemplateRepository
	personalTemplateRepo repository.PersonalTemplateRepository
}

// NewOnlineTemplateService 创建在线模板服务实例
func NewOnlineTemplateService(onlineTemplateRepo repository.OnlineTemplateRepository, personalTemplateRepo repository.PersonalTemplateRepository) OnlineTemplateService {
	return &onlineTemplateService{
		onlineTemplateRepo:   onlineTemplateRepo,
		personalTemplateRepo: personalTemplateRepo,
	}
}

// CreateOnlineTemplate 创建在线模板
func (s *onlineTemplateService) CreateOnlineTemplate(userID int32, req *user.CreateOnlineTemplateRequest) (*user.CreateOnlineTemplateResponse, error) {
	// 验证请求数据
	if req == nil {
		return nil, errors.New("request cannot be nil")
	}

	if strings.TrimSpace(req.Name) == "" {
		return nil, errors.New("template name cannot be empty")
	}

	if strings.TrimSpace(req.Template) == "" {
		return nil, errors.New("template content cannot be empty")
	}

	// 检查名称是否已存在
	exists, err := s.onlineTemplateRepo.CheckNameExists(req.Name, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to check name existence: %w", err)
	}
	if exists {
		return nil, errors.New("template name already exists")
	}

	// 构建模型
	now := time.Now()
	template := &model.OnlineTemplate{
		Name:      req.Name,
		Template:  req.Template,
		CreatedBy: userID,
		CreatedAt: &now,
		UpdatedAt: &now,
	}

	// 处理标签 - 将字符串数组转换为PostgreSQL数组格式
	if len(req.Tags) > 0 {
		// 构建PostgreSQL数组格式：{tag1,tag2,tag3}
		tagsArray := fmt.Sprintf("{%s}", strings.Join(req.Tags, ","))
		template.Tags = &tagsArray
	}

	// 设置默认值
	isDeleted := false
	template.IsDeleted = &isDeleted

	downloadCount := int32(0)
	template.DownloadCount = &downloadCount

	// 处理公开性
	isPublic := true
	if req.IsPublic != nil {
		isPublic = *req.IsPublic
	}
	template.IsPublic = &isPublic

	// 默认不是精选
	isFeatured := false
	template.IsFeatured = &isFeatured

	// 创建模板
	if err := s.onlineTemplateRepo.Create(template); err != nil {
		return nil, fmt.Errorf("failed to create template: %w", err)
	}

	// 返回响应
	return &user.CreateOnlineTemplateResponse{}, nil
}

// UpdateOnlineTemplate 更新在线模板
func (s *onlineTemplateService) UpdateOnlineTemplate(userID int32, req *user.UpdateOnlineTemplateRequest) (*user.UpdateOnlineTemplateResponse, error) {
	// 验证请求数据
	if req == nil {
		return nil, errors.New("request cannot be nil")
	}

	if req.ID <= 0 {
		return nil, errors.New("invalid template ID")
	}

	// 获取现有模板
	existingTemplate, err := s.onlineTemplateRepo.GetByIDForUser(req.ID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	// 检查名称重复（如果更新了名称）
	if req.Name != nil && *req.Name != existingTemplate.Name {
		if strings.TrimSpace(*req.Name) == "" {
			return nil, errors.New("template name cannot be empty")
		}
		exists, err := s.onlineTemplateRepo.CheckNameExists(*req.Name, userID, req.ID)
		if err != nil {
			return nil, fmt.Errorf("failed to check name existence: %w", err)
		}
		if exists {
			return nil, errors.New("template name already exists")
		}
	}

	// 更新模型字段
	if err := s.updateModelFromRequest(existingTemplate, req); err != nil {
		return nil, fmt.Errorf("failed to update model: %w", err)
	}

	// 更新数据库
	if err := s.onlineTemplateRepo.Update(existingTemplate); err != nil {
		return nil, fmt.Errorf("failed to update template: %w", err)
	}

	// 返回响应
	return &user.UpdateOnlineTemplateResponse{}, nil
}

// DeleteOnlineTemplate 批量删除在线模板
func (s *onlineTemplateService) DeleteOnlineTemplate(userID int32, req *user.DeleteOnlineTemplateRequest) (*user.DeleteOnlineTemplateResponse, error) {
	// 验证请求数据
	if req == nil {
		return nil, errors.New("request cannot be nil")
	}

	if len(req.IDs) == 0 {
		return nil, errors.New("template IDs cannot be empty")
	}

	// 验证所有ID
	for _, id := range req.IDs {
		if id <= 0 {
			return nil, fmt.Errorf("invalid template ID: %d", id)
		}
	}

	var successCount, failedCount int32
	var failedIDs []int32

	// 批量删除模板
	for _, id := range req.IDs {
		if err := s.onlineTemplateRepo.Delete(id, userID); err != nil {
			failedCount++
			failedIDs = append(failedIDs, id)
		} else {
			successCount++
		}
	}

	// 构建响应消息
	message := fmt.Sprintf("批量删除完成：成功 %d 个，失败 %d 个", successCount, failedCount)
	if failedCount == 0 {
		message = fmt.Sprintf("成功删除 %d 个在线模板", successCount)
	}

	return &user.DeleteOnlineTemplateResponse{
		Message:      message,
		SuccessCount: successCount,
		FailedCount:  failedCount,
		FailedIDs:    failedIDs,
	}, nil
}

// GetOnlineTemplate 获取在线模板详情
func (s *onlineTemplateService) GetOnlineTemplate(req *user.GetOnlineTemplateRequest) (*user.GetOnlineTemplateResponse, error) {
	// 验证请求数据
	if req == nil {
		return nil, errors.New("request cannot be nil")
	}

	if req.ID <= 0 {
		return nil, errors.New("invalid template ID")
	}

	// 获取模板（公开访问，不验证权限）
	template, err := s.onlineTemplateRepo.GetByID(req.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	return &user.GetOnlineTemplateResponse{
		OnlineTemplate: s.modelToDetail(template),
	}, nil
}

// GetOnlineTemplateList 获取在线模板列表
func (s *onlineTemplateService) GetOnlineTemplateList(req *user.GetOnlineTemplateListRequest) (*user.GetOnlineTemplateListResponse, error) {
	// 验证请求数据
	if req == nil {
		return nil, errors.New("request cannot be nil")
	}

	// 设置默认值
	page := req.Page
	if page <= 0 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}
	if pageSize > 100 {
		pageSize = 100
	}

	offset := (page - 1) * pageSize

	// 构建过滤器
	filters := repository.OnlineTemplateFilters{
		Name:       req.Name,
		Tags:       req.Tags,
		CreatedBy:  req.CreatedBy,
		IsPublic:   req.IsPublic,
		IsFeatured: req.IsFeatured,
		SortBy:     req.SortBy,
		Order:      req.Order,
	}

	// 获取模板列表
	templates, total, err := s.onlineTemplateRepo.GetList(offset, pageSize, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to get template list: %w", err)
	}

	// 转换响应
	items := make([]user.OnlineTemplateItem, len(templates))
	for i, template := range templates {
		items[i] = s.modelToItem(template)
	}

	return &user.GetOnlineTemplateListResponse{
		OnlineTemplates: items,
		Total:           total,
		Page:            page,
		PageSize:        pageSize,
	}, nil
}

// DownloadOnlineTemplate 下载在线模板
func (s *onlineTemplateService) DownloadOnlineTemplate(userID int32, req *user.DownloadOnlineTemplateRequest) (*user.DownloadOnlineTemplateResponse, error) {
	// 验证请求数据
	if req == nil {
		return nil, errors.New("request cannot be nil")
	}

	if req.ID <= 0 {
		return nil, errors.New("invalid template ID")
	}

	// 获取在线模板
	onlineTemplate, err := s.onlineTemplateRepo.GetByID(req.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	// 检查是否可以访问（公开或者是创建者）
	if onlineTemplate.IsPublic != nil && !*onlineTemplate.IsPublic && onlineTemplate.CreatedBy != userID {
		return nil, errors.New("template is not public and you are not the creator")
	}

	// 构建个人模板名称，如果已存在则添加后缀
	templateName := onlineTemplate.Name
	originalName := templateName
	counter := 1
	for {
		exists, err := s.personalTemplateRepo.CheckNameExists(templateName, userID)
		if err != nil {
			return nil, fmt.Errorf("failed to check personal template name existence: %w", err)
		}
		if !exists {
			break
		}
		templateName = fmt.Sprintf("%s (%d)", originalName, counter)
		counter++
	}

	// 创建个人模板
	now := time.Now()
	personalTemplate := &model.PersonalTemplate{
		Name:      templateName,
		Template:  onlineTemplate.Template,
		UserID:    userID,
		Tags:      onlineTemplate.Tags,
		CreatedAt: &now,
		UpdatedAt: &now,
	}

	// 设置默认值
	isDeleted := false
	personalTemplate.IsDeleted = &isDeleted

	// 保存到个人模板表
	if err := s.personalTemplateRepo.Create(personalTemplate); err != nil {
		return nil, fmt.Errorf("failed to create personal template: %w", err)
	}

	// 增加在线模板的下载计数
	if err := s.onlineTemplateRepo.IncrementDownloadCount(req.ID); err != nil {
		// 下载计数失败不影响下载功能，只记录错误
		// 可以考虑记录日志
	}

	return &user.DownloadOnlineTemplateResponse{}, nil
}

// 辅助函数：更新模型字段
func (s *onlineTemplateService) updateModelFromRequest(template *model.OnlineTemplate, req *user.UpdateOnlineTemplateRequest) error {
	now := time.Now()
	template.UpdatedAt = &now

	if req.Name != nil {
		template.Name = *req.Name
	}

	if req.Template != nil {
		if strings.TrimSpace(*req.Template) == "" {
			return errors.New("template content cannot be empty")
		}
		template.Template = *req.Template
	}

	if req.Tags != nil {
		if len(*req.Tags) > 0 {
			// 构建PostgreSQL数组格式：{tag1,tag2,tag3}
			tagsArray := fmt.Sprintf("{%s}", strings.Join(*req.Tags, ","))
			template.Tags = &tagsArray
		} else {
			template.Tags = nil
		}
	}

	if req.IsPublic != nil {
		template.IsPublic = req.IsPublic
	}

	if req.IsFeatured != nil {
		// 注意：在实际应用中，可能需要管理员权限才能设置精选状态
		template.IsFeatured = req.IsFeatured
	}

	return nil
}

// 辅助函数：模型转换为详情
func (s *onlineTemplateService) modelToDetail(template *model.OnlineTemplate) user.OnlineTemplateDetail {
	detail := user.OnlineTemplateDetail{
		ID:            template.ID,
		Name:          template.Name,
		Template:      template.Template,
		CreatedBy:     template.CreatedBy,
		DownloadCount: 0,
		IsPublic:      true,
		IsFeatured:    false,
		Tags:          []string{},
	}

	if template.CreatedAt != nil {
		detail.CreatedAt = *template.CreatedAt
	}
	if template.UpdatedAt != nil {
		detail.UpdatedAt = *template.UpdatedAt
	}
	if template.DownloadCount != nil {
		detail.DownloadCount = *template.DownloadCount
	}
	if template.IsPublic != nil {
		detail.IsPublic = *template.IsPublic
	}
	if template.IsFeatured != nil {
		detail.IsFeatured = *template.IsFeatured
	}

	// 解析标签 - 从PostgreSQL数组格式解析
	if template.Tags != nil && *template.Tags != "" && *template.Tags != "{}" {
		// PostgreSQL数组格式：{tag1,tag2,tag3}
		tagsStr := *template.Tags
		if strings.HasPrefix(tagsStr, "{") && strings.HasSuffix(tagsStr, "}") {
			// 移除大括号并分割
			tagsStr = strings.Trim(tagsStr, "{}")
			if tagsStr != "" {
				detail.Tags = strings.Split(tagsStr, ",")
			}
		}
	}

	return detail
}

// 辅助函数：模型转换为列表项
func (s *onlineTemplateService) modelToItem(template *model.OnlineTemplate) user.OnlineTemplateItem {
	item := user.OnlineTemplateItem{
		ID:            template.ID,
		Name:          template.Name,
		CreatedBy:     template.CreatedBy,
		DownloadCount: 0,
		IsPublic:      true,
		IsFeatured:    false,
		Tags:          []string{},
	}

	if template.CreatedAt != nil {
		item.CreatedAt = *template.CreatedAt
	}
	if template.UpdatedAt != nil {
		item.UpdatedAt = *template.UpdatedAt
	}
	if template.DownloadCount != nil {
		item.DownloadCount = *template.DownloadCount
	}
	if template.IsPublic != nil {
		item.IsPublic = *template.IsPublic
	}
	if template.IsFeatured != nil {
		item.IsFeatured = *template.IsFeatured
	}

	// 解析标签 - 从PostgreSQL数组格式解析
	if template.Tags != nil && *template.Tags != "" && *template.Tags != "{}" {
		// PostgreSQL数组格式：{tag1,tag2,tag3}
		tagsStr := *template.Tags
		if strings.HasPrefix(tagsStr, "{") && strings.HasSuffix(tagsStr, "}") {
			// 移除大括号并分割
			tagsStr = strings.Trim(tagsStr, "{}")
			if tagsStr != "" {
				item.Tags = strings.Split(tagsStr, ",")
			}
		}
	}

	return item
}
