package user

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"fp-browser/api/v1/user"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
)

// PersonalTemplateService 个人模板服务接口
type PersonalTemplateService interface {
	CreatePersonalTemplate(userID int32, req *user.CreatePersonalTemplateRequest) (*user.CreatePersonalTemplateResponse, error)
	UpdatePersonalTemplate(userID int32, req *user.UpdatePersonalTemplateRequest) (*user.UpdatePersonalTemplateResponse, error)
	DeletePersonalTemplate(userID int32, req *user.DeletePersonalTemplateRequest) (*user.DeletePersonalTemplateResponse, error)
	GetPersonalTemplate(userID int32, req *user.GetPersonalTemplateRequest) (*user.GetPersonalTemplateResponse, error)
	GetPersonalTemplateList(userID int32, req *user.GetPersonalTemplateListRequest) (*user.GetPersonalTemplateListResponse, error)
}

type personalTemplateService struct {
	personalTemplateRepo repository.PersonalTemplateRepository
}

// NewPersonalTemplateService 创建个人模板服务实例
func NewPersonalTemplateService(personalTemplateRepo repository.PersonalTemplateRepository) PersonalTemplateService {
	return &personalTemplateService{
		personalTemplateRepo: personalTemplateRepo,
	}
}

// CreatePersonalTemplate 创建个人模板
func (s *personalTemplateService) CreatePersonalTemplate(userID int32, req *user.CreatePersonalTemplateRequest) (*user.CreatePersonalTemplateResponse, error) {
	// 验证请求数据
	if req == nil {
		return nil, errors.New("request cannot be nil")
	}

	if strings.TrimSpace(req.Name) == "" {
		return nil, errors.New("template name cannot be empty")
	}

	if strings.TrimSpace(req.Template) == "" {
		return nil, errors.New("template content cannot be empty")
	}

	// 检查名称是否已存在
	exists, err := s.personalTemplateRepo.CheckNameExists(req.Name, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to check name existence: %w", err)
	}
	if exists {
		return nil, errors.New("template name already exists")
	}

	// 构建模型
	now := time.Now()
	template := &model.PersonalTemplate{
		Name:      req.Name,
		Template:  req.Template,
		UserID:    userID,
		CreatedAt: &now,
		UpdatedAt: &now,
	}

	// 处理标签 - 将字符串数组转换为PostgreSQL数组格式
	if len(req.Tags) > 0 {
		// 构建PostgreSQL数组格式：{tag1,tag2,tag3}
		tagsArray := fmt.Sprintf("{%s}", strings.Join(req.Tags, ","))
		template.Tags = &tagsArray
	}

	// 设置默认值
	isDeleted := false
	template.IsDeleted = &isDeleted

	// 创建模板
	if err := s.personalTemplateRepo.Create(template); err != nil {
		return nil, fmt.Errorf("failed to create template: %w", err)
	}

	// 返回响应
	return &user.CreatePersonalTemplateResponse{}, nil
}

// UpdatePersonalTemplate 更新个人模板
func (s *personalTemplateService) UpdatePersonalTemplate(userID int32, req *user.UpdatePersonalTemplateRequest) (*user.UpdatePersonalTemplateResponse, error) {
	// 验证请求数据
	if req == nil {
		return nil, errors.New("request cannot be nil")
	}

	if req.ID <= 0 {
		return nil, errors.New("invalid template ID")
	}

	// 获取现有模板
	existingTemplate, err := s.personalTemplateRepo.GetByID(req.ID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	// 检查名称重复（如果更新了名称）
	if req.Name != nil && *req.Name != existingTemplate.Name {
		if strings.TrimSpace(*req.Name) == "" {
			return nil, errors.New("template name cannot be empty")
		}
		exists, err := s.personalTemplateRepo.CheckNameExists(*req.Name, userID, req.ID)
		if err != nil {
			return nil, fmt.Errorf("failed to check name existence: %w", err)
		}
		if exists {
			return nil, errors.New("template name already exists")
		}
	}

	// 更新模型字段
	if err := s.updateModelFromRequest(existingTemplate, req); err != nil {
		return nil, fmt.Errorf("failed to update model: %w", err)
	}

	// 更新数据库
	if err := s.personalTemplateRepo.Update(existingTemplate); err != nil {
		return nil, fmt.Errorf("failed to update template: %w", err)
	}

	// 返回响应
	return &user.UpdatePersonalTemplateResponse{}, nil
}

// DeletePersonalTemplate 批量删除个人模板
func (s *personalTemplateService) DeletePersonalTemplate(userID int32, req *user.DeletePersonalTemplateRequest) (*user.DeletePersonalTemplateResponse, error) {
	// 验证请求数据
	if req == nil {
		return nil, errors.New("request cannot be nil")
	}

	if len(req.IDs) == 0 {
		return nil, errors.New("template IDs cannot be empty")
	}

	// 验证所有ID
	for _, id := range req.IDs {
		if id <= 0 {
			return nil, fmt.Errorf("invalid template ID: %d", id)
		}
	}

	var successCount, failedCount int32
	var failedIDs []int32

	// 批量删除模板
	for _, id := range req.IDs {
		if err := s.personalTemplateRepo.Delete(id, userID); err != nil {
			failedCount++
			failedIDs = append(failedIDs, id)
		} else {
			successCount++
		}
	}

	// 构建响应消息
	message := fmt.Sprintf("批量删除完成：成功 %d 个，失败 %d 个", successCount, failedCount)
	if failedCount == 0 {
		message = fmt.Sprintf("成功删除 %d 个个人模板", successCount)
	}

	return &user.DeletePersonalTemplateResponse{
		Message:      message,
		SuccessCount: successCount,
		FailedCount:  failedCount,
		FailedIDs:    failedIDs,
	}, nil
}

// GetPersonalTemplate 获取个人模板详情
func (s *personalTemplateService) GetPersonalTemplate(userID int32, req *user.GetPersonalTemplateRequest) (*user.GetPersonalTemplateResponse, error) {
	// 验证请求数据
	if req == nil {
		return nil, errors.New("request cannot be nil")
	}

	if req.ID <= 0 {
		return nil, errors.New("invalid template ID")
	}

	// 获取模板
	template, err := s.personalTemplateRepo.GetByID(req.ID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	return &user.GetPersonalTemplateResponse{
		PersonalTemplate: s.modelToDetail(template),
	}, nil
}

// GetPersonalTemplateList 获取个人模板列表
func (s *personalTemplateService) GetPersonalTemplateList(userID int32, req *user.GetPersonalTemplateListRequest) (*user.GetPersonalTemplateListResponse, error) {
	// 验证请求数据
	if req == nil {
		return nil, errors.New("request cannot be nil")
	}

	// 设置默认值
	page := req.Page
	if page <= 0 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}
	if pageSize > 100 {
		pageSize = 100
	}

	offset := (page - 1) * pageSize

	// 获取模板列表
	templates, total, err := s.personalTemplateRepo.GetList(userID, offset, pageSize, req.Name, req.Tags)
	if err != nil {
		return nil, fmt.Errorf("failed to get template list: %w", err)
	}

	// 转换响应
	items := make([]user.PersonalTemplateItem, len(templates))
	for i, template := range templates {
		items[i] = s.modelToItem(template)
	}

	return &user.GetPersonalTemplateListResponse{
		PersonalTemplates: items,
		Total:             total,
		Page:              page,
		PageSize:          pageSize,
	}, nil
}

// 辅助函数：更新模型字段
func (s *personalTemplateService) updateModelFromRequest(template *model.PersonalTemplate, req *user.UpdatePersonalTemplateRequest) error {
	now := time.Now()
	template.UpdatedAt = &now

	if req.Name != nil {
		template.Name = *req.Name
	}

	if req.Template != nil {
		if strings.TrimSpace(*req.Template) == "" {
			return errors.New("template content cannot be empty")
		}
		template.Template = *req.Template
	}

	if req.Tags != nil {
		if len(*req.Tags) > 0 {
			// 构建PostgreSQL数组格式：{tag1,tag2,tag3}
			tagsArray := fmt.Sprintf("{%s}", strings.Join(*req.Tags, ","))
			template.Tags = &tagsArray
		} else {
			template.Tags = nil
		}
	}

	return nil
}

// 辅助函数：模型转换为详情
func (s *personalTemplateService) modelToDetail(template *model.PersonalTemplate) user.PersonalTemplateDetail {
	detail := user.PersonalTemplateDetail{
		ID:       template.ID,
		Name:     template.Name,
		Template: template.Template,
		UserID:   template.UserID,
		Tags:     []string{},
	}

	if template.CreatedAt != nil {
		detail.CreatedAt = *template.CreatedAt
	}
	if template.UpdatedAt != nil {
		detail.UpdatedAt = *template.UpdatedAt
	}

	// 解析标签 - 从PostgreSQL数组格式解析
	if template.Tags != nil && *template.Tags != "" && *template.Tags != "{}" {
		// PostgreSQL数组格式：{tag1,tag2,tag3}
		tagsStr := *template.Tags
		if strings.HasPrefix(tagsStr, "{") && strings.HasSuffix(tagsStr, "}") {
			// 移除大括号并分割
			tagsStr = strings.Trim(tagsStr, "{}")
			if tagsStr != "" {
				detail.Tags = strings.Split(tagsStr, ",")
			}
		}
	}

	return detail
}

// 辅助函数：模型转换为列表项
func (s *personalTemplateService) modelToItem(template *model.PersonalTemplate) user.PersonalTemplateItem {
	item := user.PersonalTemplateItem{
		ID:     template.ID,
		Name:   template.Name,
		UserID: template.UserID,
		Tags:   []string{},
	}

	if template.CreatedAt != nil {
		item.CreatedAt = *template.CreatedAt
	}
	if template.UpdatedAt != nil {
		item.UpdatedAt = *template.UpdatedAt
	}

	// 解析标签 - 从PostgreSQL数组格式解析
	if template.Tags != nil && *template.Tags != "" && *template.Tags != "{}" {
		// PostgreSQL数组格式：{tag1,tag2,tag3}
		tagsStr := *template.Tags
		if strings.HasPrefix(tagsStr, "{") && strings.HasSuffix(tagsStr, "}") {
			// 移除大括号并分割
			tagsStr = strings.Trim(tagsStr, "{}")
			if tagsStr != "" {
				item.Tags = strings.Split(tagsStr, ",")
			}
		}
	}

	return item
}
