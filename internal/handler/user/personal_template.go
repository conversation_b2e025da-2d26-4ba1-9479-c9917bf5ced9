package user

import (
	"strconv"

	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/service/user"
	"fp-browser/pkg/log"

	"github.com/kataras/iris/v12"
)

type PersonalTemplateHandler struct {
	*Handler
	personalTemplateService user.PersonalTemplateService
}

// NewPersonalTemplateHandler 创建个人模板处理器实例
func NewPersonalTemplateHandler(logger *log.Logger, personalTemplateService user.PersonalTemplateService) *PersonalTemplateHandler {
	return &PersonalTemplateHandler{
		Handler:                 NewHandler(logger),
		personalTemplateService: personalTemplateService,
	}
}

// CreatePersonalTemplate 创建个人模板
// @Summary 创建个人模板
// @Description 创建一个新的个人RPA模板
// @Tags PersonalTemplate
// @Accept json
// @Produce json
// @Param request body v1.CreatePersonalTemplateRequest true "创建个人模板请求"
// @Success 200 {object} v1.CreatePersonalTemplateResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/personal-templates [post]
func (h *PersonalTemplateHandler) CreatePersonalTemplate(ctx iris.Context) {
	// 获取用户ID
	userID, _, _, _, _, ok := h.RequireAuth(ctx)
	if !ok {
		return
	}

	// 解析请求参数
	var request v1.CreatePersonalTemplateRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 调用服务层
	_, err := h.personalTemplateService.CreatePersonalTemplate(userID, &request)
	if err != nil {
		h.logger.Error("创建个人模板失败",
			"userID", userID,
			"name", request.Name,
			"error", err)
		h.HandleInternalError(ctx, err, "创建个人模板失败")
		return
	}

	h.logger.Info("个人模板创建成功",
		"userID", userID,
		"name", request.Name)

	response := v1.Response{
		Code:    200,
		Message: "个人模板创建成功",
		Data:    nil,
	}
	_ = ctx.JSON(response)
}

// UpdatePersonalTemplate 更新个人模板
// @Summary 更新个人模板
// @Description 更新指定的个人RPA模板
// @Tags PersonalTemplate
// @Accept json
// @Produce json
// @Param request body v1.UpdatePersonalTemplateRequest true "更新个人模板请求"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 404 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/personal-templates [put]
func (h *PersonalTemplateHandler) UpdatePersonalTemplate(ctx iris.Context) {
	// 获取用户ID
	userID, _, _, _, _, ok := h.RequireAuth(ctx)
	if !ok {
		return
	}

	// 解析请求参数
	var request v1.UpdatePersonalTemplateRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 调用服务层
	_, err := h.personalTemplateService.UpdatePersonalTemplate(userID, &request)
	if err != nil {
		h.logger.Error("更新个人模板失败",
			"userID", userID,
			"id", request.ID,
			"error", err)
		h.HandleInternalError(ctx, err, "更新个人模板失败")
		return
	}

	h.logger.Info("个人模板更新成功",
		"userID", userID,
		"id", request.ID)

	response := v1.Response{
		Code:    200,
		Message: "个人模板更新成功",
		Data:    nil,
	}
	_ = ctx.JSON(response)
}

// DeletePersonalTemplate 批量删除个人模板
// @Summary 批量删除个人模板
// @Description 批量删除指定的个人RPA模板，支持一次删除多个模板
// @Tags PersonalTemplate
// @Accept json
// @Produce json
// @Param request body v1.DeletePersonalTemplateRequest true "批量删除个人模板请求"
// @Success 200 {object} v1.DeletePersonalTemplateResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 404 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/personal-templates [delete]
func (h *PersonalTemplateHandler) DeletePersonalTemplate(ctx iris.Context) {
	// 获取用户ID
	userID, _, _, _, _, ok := h.RequireAuth(ctx)
	if !ok {
		return
	}

	// 解析请求参数
	var request v1.DeletePersonalTemplateRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 调用服务层
	resp, err := h.personalTemplateService.DeletePersonalTemplate(userID, &request)
	if err != nil {
		h.logger.Error("批量删除个人模板失败",
			"userID", userID,
			"ids", request.IDs,
			"error", err)
		h.HandleInternalError(ctx, err, "批量删除个人模板失败")
		return
	}

	h.logger.Info("批量删除个人模板完成",
		"userID", userID,
		"ids", request.IDs,
		"successCount", resp.SuccessCount,
		"failedCount", resp.FailedCount,
		"failedIDs", resp.FailedIDs)

	response := v1.Response{
		Code:    200,
		Message: resp.Message,
		Data:    resp,
	}
	_ = ctx.JSON(response)
}

// GetPersonalTemplate 获取个人模板详情
// @Summary 获取个人模板详情
// @Description 根据ID获取个人RPA模板的详细信息
// @Tags PersonalTemplate
// @Accept json
// @Produce json
// @Param id path int true "模板ID"
// @Success 200 {object} v1.GetPersonalTemplateResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 404 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/personal-templates/{id} [get]
func (h *PersonalTemplateHandler) GetPersonalTemplate(ctx iris.Context) {
	// 获取用户ID
	userID, _, _, _, _, ok := h.RequireAuth(ctx)
	if !ok {
		return
	}

	// 获取路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseInt(idStr, 10, 32)
	if err != nil {
		h.HandleBadRequest(ctx, err, "无效的模板ID")
		return
	}

	// 构建请求
	request := &v1.GetPersonalTemplateRequest{
		ID: int32(id),
	}

	// 调用服务层
	resp, err := h.personalTemplateService.GetPersonalTemplate(userID, request)
	if err != nil {
		h.logger.Error("获取个人模板失败",
			"userID", userID,
			"id", id,
			"error", err)
		h.HandleInternalError(ctx, err, "获取个人模板失败")
		return
	}

	response := v1.Response{
		Code:    200,
		Message: "获取个人模板成功",
		Data:    resp.PersonalTemplate,
	}
	_ = ctx.JSON(response)
}

// GetPersonalTemplateList 获取个人模板列表
// @Summary 获取个人模板列表
// @Description 获取当前用户的个人RPA模板列表，支持分页和筛选
// @Tags PersonalTemplate
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param name query string false "模板名称筛选"
// @Success 200 {object} v1.GetPersonalTemplateListResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/personal-templates [get]
func (h *PersonalTemplateHandler) GetPersonalTemplateList(ctx iris.Context) {
	// 获取用户ID
	userID, _, _, _, _, ok := h.RequireAuth(ctx)
	if !ok {
		return
	}

	// 解析查询参数
	request := &v1.GetPersonalTemplateListRequest{}

	// 分页参数
	if pageStr := ctx.URLParam("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil {
			request.Page = page
		}
	}

	if pageSizeStr := ctx.URLParam("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil {
			request.PageSize = pageSize
		}
	}

	// 筛选参数
	request.Name = ctx.URLParam("name")

	// 调用服务层
	resp, err := h.personalTemplateService.GetPersonalTemplateList(userID, request)
	if err != nil {
		h.logger.Error("获取个人模板列表失败",
			"userID", userID,
			"error", err)
		h.HandleInternalError(ctx, err, "获取个人模板列表失败")
		return
	}

	response := v1.Response{
		Code:    200,
		Message: "获取个人模板列表成功",
		Data: v1.GetPersonalTemplateListResponse{
			PersonalTemplates: resp.PersonalTemplates,
			Total:             resp.Total,
			Page:              resp.Page,
			PageSize:          resp.PageSize,
		},
	}
	_ = ctx.JSON(response)
}
