package user

import (
	"strconv"

	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/service/user"
	"fp-browser/pkg/log"

	"github.com/kataras/iris/v12"
)

type OnlineTemplateHandler struct {
	*Handler
	onlineTemplateService user.OnlineTemplateService
}

// NewOnlineTemplateHandler 创建在线模板处理器实例
func NewOnlineTemplateHandler(logger *log.Logger, onlineTemplateService user.OnlineTemplateService) *OnlineTemplateHandler {
	return &OnlineTemplateHandler{
		Handler:               NewHandler(logger),
		onlineTemplateService: onlineTemplateService,
	}
}

// CreateOnlineTemplate 创建在线模板
// @Summary 创建在线模板
// @Description 创建一个新的在线RPA模板
// @Tags OnlineTemplate
// @Accept json
// @Produce json
// @Param request body v1.CreateOnlineTemplateRequest true "创建在线模板请求"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/online-templates [post]
func (h *OnlineTemplateHandler) CreateOnlineTemplate(ctx iris.Context) {
	// 获取用户ID
	userID, _, _, _, _, ok := h.RequireAuth(ctx)
	if !ok {
		return
	}

	// 解析请求参数
	var request v1.CreateOnlineTemplateRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 调用服务层
	_, err := h.onlineTemplateService.CreateOnlineTemplate(userID, &request)
	if err != nil {
		h.logger.Error("创建在线模板失败",
			"userID", userID,
			"name", request.Name,
			"error", err)
		h.HandleInternalError(ctx, err, "创建在线模板失败")
		return
	}

	h.logger.Info("在线模板创建成功",
		"userID", userID,
		"name", request.Name)

	response := v1.Response{
		Code:    200,
		Message: "在线模板创建成功",
		Data:    nil,
	}
	_ = ctx.JSON(response)
}

// UpdateOnlineTemplate 更新在线模板
// @Summary 更新在线模板
// @Description 更新指定的在线RPA模板
// @Tags OnlineTemplate
// @Accept json
// @Produce json
// @Param request body v1.UpdateOnlineTemplateRequest true "更新在线模板请求"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 404 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/online-templates [put]
func (h *OnlineTemplateHandler) UpdateOnlineTemplate(ctx iris.Context) {
	// 获取用户ID
	userID, _, _, _, _, ok := h.RequireAuth(ctx)
	if !ok {
		return
	}

	// 解析请求参数
	var request v1.UpdateOnlineTemplateRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 调用服务层
	_, err := h.onlineTemplateService.UpdateOnlineTemplate(userID, &request)
	if err != nil {
		h.logger.Error("更新在线模板失败",
			"userID", userID,
			"id", request.ID,
			"error", err)
		h.HandleInternalError(ctx, err, "更新在线模板失败")
		return
	}

	h.logger.Info("在线模板更新成功",
		"userID", userID,
		"id", request.ID)

	response := v1.Response{
		Code:    200,
		Message: "在线模板更新成功",
		Data:    nil,
	}
	_ = ctx.JSON(response)
}

// DeleteOnlineTemplate 删除在线模板
// @Summary 删除在线模板
// @Description 删除指定的在线RPA模板
// @Tags OnlineTemplate
// @Accept json
// @Produce json
// @Param request body v1.DeleteOnlineTemplateRequest true "删除在线模板请求"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 404 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/online-templates [delete]
func (h *OnlineTemplateHandler) DeleteOnlineTemplate(ctx iris.Context) {
	// 获取用户ID
	userID, _, _, _, _, ok := h.RequireAuth(ctx)
	if !ok {
		return
	}

	// 解析请求参数
	var request v1.DeleteOnlineTemplateRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 调用服务层
	resp, err := h.onlineTemplateService.DeleteOnlineTemplate(userID, &request)
	if err != nil {
		h.logger.Error("批量删除在线模板失败",
			"userID", userID,
			"ids", request.IDs,
			"error", err)
		h.HandleInternalError(ctx, err, "批量删除在线模板失败")
		return
	}

	h.logger.Info("批量删除在线模板完成",
		"userID", userID,
		"ids", request.IDs,
		"successCount", resp.SuccessCount,
		"failedCount", resp.FailedCount,
		"failedIDs", resp.FailedIDs)

	response := v1.Response{
		Code:    200,
		Message: resp.Message,
		Data:    resp,
	}
	_ = ctx.JSON(response)
}

// GetOnlineTemplate 获取在线模板详情
// @Summary 获取在线模板详情
// @Description 根据ID获取在线RPA模板的详细信息
// @Tags OnlineTemplate
// @Accept json
// @Produce json
// @Param id path int true "模板ID"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 404 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/online-templates/{id} [get]
func (h *OnlineTemplateHandler) GetOnlineTemplate(ctx iris.Context) {
	// 获取路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseInt(idStr, 10, 32)
	if err != nil {
		h.HandleBadRequest(ctx, err, "无效的模板ID")
		return
	}

	// 构建请求
	request := &v1.GetOnlineTemplateRequest{
		ID: int32(id),
	}

	// 调用服务层（公开访问，不需要验证用户）
	resp, err := h.onlineTemplateService.GetOnlineTemplate(request)
	if err != nil {
		h.logger.Error("获取在线模板失败",
			"id", id,
			"error", err)
		h.HandleInternalError(ctx, err, "获取在线模板失败")
		return
	}

	response := v1.Response{
		Code:    200,
		Message: "获取在线模板成功",
		Data:    resp.OnlineTemplate,
	}
	_ = ctx.JSON(response)
}

// GetOnlineTemplateList 获取在线模板列表
// @Summary 获取在线模板列表
// @Description 获取在线RPA模板列表，支持分页和筛选
// @Tags OnlineTemplate
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param name query string false "模板名称筛选"
// @Param created_by query int false "创建者ID筛选"
// @Param is_public query bool false "是否公开筛选"
// @Param is_featured query bool false "是否精选筛选"
// @Param sort_by query string false "排序字段：created_at, download_count"
// @Param order query string false "排序方向：asc, desc"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/online-templates [get]
func (h *OnlineTemplateHandler) GetOnlineTemplateList(ctx iris.Context) {
	// 解析查询参数
	request := &v1.GetOnlineTemplateListRequest{}

	// 分页参数
	if pageStr := ctx.URLParam("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil {
			request.Page = page
		}
	}

	if pageSizeStr := ctx.URLParam("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil {
			request.PageSize = pageSize
		}
	}

	// 筛选参数
	request.Name = ctx.URLParam("name")
	request.SortBy = ctx.URLParam("sort_by")
	request.Order = ctx.URLParam("order")

	if createdByStr := ctx.URLParam("created_by"); createdByStr != "" {
		if createdBy, err := strconv.ParseInt(createdByStr, 10, 32); err == nil {
			request.CreatedBy = int32(createdBy)
		}
	}

	// 布尔参数
	if isPublicStr := ctx.URLParam("is_public"); isPublicStr != "" {
		if isPublicStr == "true" {
			isPublic := true
			request.IsPublic = &isPublic
		} else if isPublicStr == "false" {
			isPublic := false
			request.IsPublic = &isPublic
		}
	}

	if isFeaturedStr := ctx.URLParam("is_featured"); isFeaturedStr != "" {
		if isFeaturedStr == "true" {
			isFeatured := true
			request.IsFeatured = &isFeatured
		} else if isFeaturedStr == "false" {
			isFeatured := false
			request.IsFeatured = &isFeatured
		}
	}

	// 调用服务层
	resp, err := h.onlineTemplateService.GetOnlineTemplateList(request)
	if err != nil {
		h.logger.Error("获取在线模板列表失败", "error", err)
		h.HandleInternalError(ctx, err, "获取在线模板列表失败")
		return
	}

	response := v1.Response{
		Code:    200,
		Message: "获取在线模板列表成功",
		Data: v1.GetOnlineTemplateListResponse{
			OnlineTemplates: resp.OnlineTemplates,
			Total:           resp.Total,
			Page:            resp.Page,
			PageSize:        resp.PageSize,
		},
	}
	_ = ctx.JSON(response)
}

// DownloadOnlineTemplate 下载在线模板
// @Summary 下载在线模板
// @Description 将指定的在线RPA模板添加到用户的个人模板库中，如果名称重复会自动添加后缀
// @Tags OnlineTemplate
// @Accept json
// @Produce json
// @Param request body v1.DownloadOnlineTemplateRequest true "下载在线模板请求"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 403 {object} v1.Response
// @Failure 404 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/online-templates/download [post]
func (h *OnlineTemplateHandler) DownloadOnlineTemplate(ctx iris.Context) {
	// 获取用户ID
	userID, _, _, _, _, ok := h.RequireAuth(ctx)
	if !ok {
		return
	}

	// 解析请求参数
	var request v1.DownloadOnlineTemplateRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 调用服务层
	_, err := h.onlineTemplateService.DownloadOnlineTemplate(userID, &request)
	if err != nil {
		h.logger.Error("下载在线模板失败",
			"userID", userID,
			"id", request.ID,
			"error", err)
		if err.Error() == "template is not public and you are not the creator" {
			h.HandleForbidden(ctx, err, "无权限下载此模板")
		} else {
			h.HandleInternalError(ctx, err, "下载在线模板失败")
		}
		return
	}

	h.logger.Info("在线模板下载成功",
		"userID", userID,
		"id", request.ID)

	response := v1.Response{
		Code:    200,
		Message: "模板已成功添加到个人模板库",
		Data:    nil,
	}
	_ = ctx.JSON(response)
}
