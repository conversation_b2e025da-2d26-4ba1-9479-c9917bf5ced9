package user

import "time"

// CreatePersonalTemplateRequest 创建个人模板请求
type CreatePersonalTemplateRequest struct {
	Name     string   `json:"name" validate:"required,max=100"` // 模板名称
	Template string   `json:"template" validate:"required"`     // YAML配置文件内容
	Tags     []string `json:"tags,omitempty"`                   // 标签数组
}

// CreatePersonalTemplateResponse 创建个人模板响应 (空结构体，使用标准Response格式)
type CreatePersonalTemplateResponse struct{}

// UpdatePersonalTemplateRequest 更新个人模板请求
type UpdatePersonalTemplateRequest struct {
	ID       int32     `json:"id" validate:"required"`                      // 模板ID
	Name     *string   `json:"name,omitempty" validate:"omitempty,max=100"` // 模板名称
	Template *string   `json:"template,omitempty"`                          // YAML配置文件内容
	Tags     *[]string `json:"tags,omitempty"`                              // 标签数组
}

// UpdatePersonalTemplateResponse 更新个人模板响应 (空结构体，使用标准Response格式)
type UpdatePersonalTemplateResponse struct{}

// DeletePersonalTemplateRequest 删除个人模板请求
type DeletePersonalTemplateRequest struct {
	IDs []int32 `json:"ids" validate:"required,min=1"` // 模板ID列表，支持批量删除
}

// DeletePersonalTemplateResponse 删除个人模板响应
type DeletePersonalTemplateResponse struct {
	Message      string  `json:"message"`
	SuccessCount int32   `json:"success_count"` // 成功删除的数量
	FailedCount  int32   `json:"failed_count"`  // 删除失败的数量
	FailedIDs    []int32 `json:"failed_ids"`    // 删除失败的ID列表
}

// GetPersonalTemplateRequest 获取个人模板请求
type GetPersonalTemplateRequest struct {
	ID int32 `json:"id" validate:"required"` // 模板ID
}

// GetPersonalTemplateResponse 获取个人模板响应
type GetPersonalTemplateResponse struct {
	PersonalTemplate PersonalTemplateDetail `json:"personal_template"`
}

// GetPersonalTemplateListRequest 获取个人模板列表请求
type GetPersonalTemplateListRequest struct {
	Page     int      `json:"page" validate:"min=1"`              // 页码，默认1
	PageSize int      `json:"page_size" validate:"min=1,max=100"` // 每页数量，默认10
	Name     string   `json:"name,omitempty"`                     // 模板名称筛选
	Tags     []string `json:"tags,omitempty"`                     // 标签筛选
}

// GetPersonalTemplateListResponse 获取个人模板列表响应
type GetPersonalTemplateListResponse struct {
	PersonalTemplates []PersonalTemplateItem `json:"personal_templates"`
	Total             int64                  `json:"total"`
	Page              int                    `json:"page"`
	PageSize          int                    `json:"page_size"`
}

// PersonalTemplateItem 个人模板列表项
type PersonalTemplateItem struct {
	ID        int32     `json:"id"`
	Name      string    `json:"name"`
	Tags      []string  `json:"tags"`
	UserID    int32     `json:"user_id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// PersonalTemplateDetail 个人模板详情
type PersonalTemplateDetail struct {
	ID        int32     `json:"id"`
	Name      string    `json:"name"`
	Template  string    `json:"template"`
	Tags      []string  `json:"tags"`
	UserID    int32     `json:"user_id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
