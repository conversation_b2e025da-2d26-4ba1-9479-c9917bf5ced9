package user

import "time"

// CreateOnlineTemplateRequest 创建在线模板请求
type CreateOnlineTemplateRequest struct {
	Name     string   `json:"name" validate:"required,max=100"` // 模板名称
	Template string   `json:"template" validate:"required"`     // YAML配置文件内容
	Tags     []string `json:"tags,omitempty"`                   // 标签数组
	IsPublic *bool    `json:"is_public,omitempty"`              // 是否公开，默认true
}

// CreateOnlineTemplateResponse 创建在线模板响应 (空结构体，使用标准Response格式)
type CreateOnlineTemplateResponse struct{}

// UpdateOnlineTemplateRequest 更新在线模板请求
type UpdateOnlineTemplateRequest struct {
	ID         int32     `json:"id" validate:"required"`                      // 模板ID
	Name       *string   `json:"name,omitempty" validate:"omitempty,max=100"` // 模板名称
	Template   *string   `json:"template,omitempty"`                          // YAML配置文件内容
	Tags       *[]string `json:"tags,omitempty"`                              // 标签数组
	IsPublic   *bool     `json:"is_public,omitempty"`                         // 是否公开
	IsFeatured *bool     `json:"is_featured,omitempty"`                       // 是否精选（仅管理员可设置）
}

// UpdateOnlineTemplateResponse 更新在线模板响应 (空结构体，使用标准Response格式)
type UpdateOnlineTemplateResponse struct{}

// DeleteOnlineTemplateRequest 删除在线模板请求
type DeleteOnlineTemplateRequest struct {
	IDs []int32 `json:"ids" validate:"required,min=1"` // 模板ID列表，支持批量删除
}

// DeleteOnlineTemplateResponse 删除在线模板响应
type DeleteOnlineTemplateResponse struct {
	Message      string  `json:"message"`
	SuccessCount int32   `json:"success_count"` // 成功删除的数量
	FailedCount  int32   `json:"failed_count"`  // 删除失败的数量
	FailedIDs    []int32 `json:"failed_ids"`    // 删除失败的ID列表
}

// GetOnlineTemplateRequest 获取在线模板请求
type GetOnlineTemplateRequest struct {
	ID int32 `json:"id" validate:"required"` // 模板ID
}

// GetOnlineTemplateResponse 获取在线模板响应
type GetOnlineTemplateResponse struct {
	OnlineTemplate OnlineTemplateDetail `json:"online_template"`
}

// GetOnlineTemplateListRequest 获取在线模板列表请求
type GetOnlineTemplateListRequest struct {
	Page       int      `json:"page" validate:"min=1"`              // 页码，默认1
	PageSize   int      `json:"page_size" validate:"min=1,max=100"` // 每页数量，默认10
	Name       string   `json:"name,omitempty"`                     // 模板名称筛选
	Tags       []string `json:"tags,omitempty"`                     // 标签筛选
	CreatedBy  int32    `json:"created_by,omitempty"`               // 创建者ID筛选
	IsPublic   *bool    `json:"is_public,omitempty"`                // 是否公开筛选
	IsFeatured *bool    `json:"is_featured,omitempty"`              // 是否精选筛选
	SortBy     string   `json:"sort_by,omitempty"`                  // 排序字段：created_at, download_count
	Order      string   `json:"order,omitempty"`                    // 排序方向：asc, desc
}

// GetOnlineTemplateListResponse 获取在线模板列表响应
type GetOnlineTemplateListResponse struct {
	OnlineTemplates []OnlineTemplateItem `json:"online_templates"`
	Total           int64                `json:"total"`
	Page            int                  `json:"page"`
	PageSize        int                  `json:"page_size"`
}

// DownloadOnlineTemplateRequest 下载在线模板请求
type DownloadOnlineTemplateRequest struct {
	ID int32 `json:"id" validate:"required"` // 模板ID
}

// DownloadOnlineTemplateResponse 下载在线模板响应 (空结构体，使用标准Response格式)
type DownloadOnlineTemplateResponse struct{}

// OnlineTemplateItem 在线模板列表项
type OnlineTemplateItem struct {
	ID            int32     `json:"id"`
	Name          string    `json:"name"`
	Tags          []string  `json:"tags"`
	CreatedBy     int32     `json:"created_by"`
	DownloadCount int32     `json:"download_count"`
	IsPublic      bool      `json:"is_public"`
	IsFeatured    bool      `json:"is_featured"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// OnlineTemplateDetail 在线模板详情
type OnlineTemplateDetail struct {
	ID            int32     `json:"id"`
	Name          string    `json:"name"`
	Template      string    `json:"template"`
	Tags          []string  `json:"tags"`
	CreatedBy     int32     `json:"created_by"`
	DownloadCount int32     `json:"download_count"`
	IsPublic      bool      `json:"is_public"`
	IsFeatured    bool      `json:"is_featured"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}
