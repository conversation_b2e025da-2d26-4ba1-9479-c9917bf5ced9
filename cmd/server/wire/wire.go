//go:build wireinject
// +build wireinject

package wire

import (
	authHandler "fp-browser/internal/handler/auth"
	sharedHandler "fp-browser/internal/handler/shared"
	userHandler "fp-browser/internal/handler/user"
	"fp-browser/internal/repository"
	"fp-browser/internal/server"
	sharedService "fp-browser/internal/service/shared"
	userService "fp-browser/internal/service/user"
	"fp-browser/pkg/app"
	"fp-browser/pkg/ip"
	"fp-browser/pkg/log"
	"fp-browser/pkg/oss"
	"fp-browser/pkg/server/http"
	"fp-browser/pkg/smtp"

	"github.com/google/wire"
	"github.com/spf13/viper"
)

var clientSet = wire.NewSet(
	oss.NewOSS,
	smtp.NewSMTP,
	ip.NewIP2Location,
)
var repositorySet = wire.NewSet(
	repository.NewDB,
	repository.NewRedis,
	repository.NewRepository,
	repository.NewAdminRepo,
	repository.NewCommissionTransactionRepository,
	repository.NewEnvironmentRepository,
	repository.NewForwardRepository,
	repository.NewGroupRepository,
	repository.NewLoginLogRepository,
	repository.NewOperationLogRepository,
	repository.NewOrderRepository,
	repository.NewProxyRepository,
	repository.NewRoleRepository,
	repository.NewSelfHostProxyRepository,
	repository.NewSubscriptionRepository,
	repository.NewSystemRepo,
	repository.NewTeamRepository,
	repository.NewUserBalanceRepository,
	repository.NewUserRepository,
	repository.NewUserSubscriptionRepository,
	repository.NewWalletTransactionRepository,
	repository.NewTeamIPWhitelistRepository,
	repository.NewPersonalTemplateRepository,
	repository.NewOnlineTemplateRepository,
)

var serviceSet = wire.NewSet(
	userService.NewService,
	userService.NewCommissionService,
	userService.NewEnvironmentService,
	userService.NewGroupService,
	userService.NewLoginLogService,
	userService.NewOperationLogService,
	userService.NewOrderService,
	userService.NewProxyService,
	userService.NewRoleService,
	userService.NewSelfHostProxyService,
	userService.NewSubscriptionService,
	userService.NewTeamService,
	userService.NewUserService,
	userService.NewUserSubscriptionService,
	userService.NewWalletService,
	userService.NewTeamIPWhitelistService,
	userService.NewPersonalTemplateService,
	userService.NewOnlineTemplateService,
	sharedService.NewRedisService,
	sharedService.NewOSSService,
	sharedService.NewCaptchaService,
	sharedService.NewEmailService,
)

var handlerSet = wire.NewSet(
	userHandler.NewHandler,
	userHandler.NewCommissionHandler,
	userHandler.NewEnvironmentHandler,
	userHandler.NewGroupHandler,
	userHandler.NewLoginLogHandler,
	userHandler.NewOperationLogHandler,
	userHandler.NewOrderHandler,
	userHandler.NewRoleHandler,
	userHandler.NewSelfHostProxyHandler,
	userHandler.NewSubscriptionHandler,
	userHandler.NewTeamHandler,
	userHandler.NewTeamIPWhitelistHandler,
	userHandler.NewUserHandler,
	userHandler.NewWalletHandler,
	userHandler.NewUserSubscriptionHandler,
	userHandler.NewPersonalTemplateHandler,
	userHandler.NewOnlineTemplateHandler,
	authHandler.NewHandler,
	authHandler.NewLoginHandler,
	authHandler.NewRegisterHandler,
	sharedHandler.NewHandler,
	sharedHandler.NewCaptchaHandler,
	sharedHandler.NewEmailHandler,
)

var serverSet = wire.NewSet(
	server.NewHTTPServer,
)

// build App
func newApp(
	httpServer *http.Server,
) *app.App {
	return app.NewApp(
		app.WithServer(httpServer),
		app.WithName("suiyu-server"),
	)
}

func NewWire(*viper.Viper, *log.Logger) (*app.App, func(), error) {
	panic(wire.Build(
		repositorySet,
		serviceSet,
		handlerSet,
		//jobSet,
		clientSet,
		serverSet,
		newApp,
	))
}
