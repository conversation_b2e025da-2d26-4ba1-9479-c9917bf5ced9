// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"fp-browser/internal/handler/auth"
	shared2 "fp-browser/internal/handler/shared"
	user2 "fp-browser/internal/handler/user"
	"fp-browser/internal/repository"
	"fp-browser/internal/server"
	"fp-browser/internal/service/shared"
	"fp-browser/internal/service/user"
	"fp-browser/pkg/app"
	"fp-browser/pkg/ip"
	"fp-browser/pkg/log"
	"fp-browser/pkg/oss"
	"fp-browser/pkg/server/http"
	"fp-browser/pkg/smtp"
	"github.com/google/wire"
	"github.com/spf13/viper"
)

// Injectors from wire.go:

func NewWire(viperViper *viper.Viper, logger *log.Logger) (*app.App, func(), error) {
	db := repository.NewDB(viperViper, logger)
	repositoryRepository := repository.NewRepository(logger, db)
	commissionTransactionRepository := repository.NewCommissionTransactionRepository(repositoryRepository)
	commissionService := user.NewCommissionService(commissionTransactionRepository)
	commissionHandler := user2.NewCommissionHandler(logger, commissionService)
	environmentRepository := repository.NewEnvironmentRepository(repositoryRepository)
	userRepository := repository.NewUserRepository(repositoryRepository, environmentRepository)
	userService := user.NewUserService(userRepository)
	client := repository.NewRedis(viperViper, logger)
	redisService := shared.NewRedisService(client)
	ossClient := oss.NewOSS(viperViper, logger)
	ossService := shared.NewOSSService(ossClient)
	environmentService := user.NewEnvironmentService(environmentRepository, userService, redisService, ossService)
	environmentHandler := user2.NewEnvironmentHandler(logger, environmentService, userService, ossService)
	groupRepository := repository.NewGroupRepository(repositoryRepository)
	groupService := user.NewGroupService(groupRepository)
	groupHandler := user2.NewGroupHandler(logger, groupService)
	loginLogRepository := repository.NewLoginLogRepository(repositoryRepository)
	loginLogService := user.NewLoginLogService(loginLogRepository)
	loginLogHandler := user2.NewLoginLogHandler(logger, loginLogService)
	operationLogRepository := repository.NewOperationLogRepository(repositoryRepository)
	operationLogService := user.NewOperationLogService(operationLogRepository)
	operationLogHandler := user2.NewOperationLogHandler(logger, operationLogService)
	orderRepository := repository.NewOrderRepository(repositoryRepository)
	orderService := user.NewOrderService(orderRepository)
	orderHandler := user2.NewOrderHandler(logger, orderService)
	roleRepository := repository.NewRoleRepository(repositoryRepository)
	roleService := user.NewRoleService(roleRepository)
	roleHandler := user2.NewRoleHandler(logger, roleService)
	selfHostProxyRepository := repository.NewSelfHostProxyRepository(repositoryRepository)
	selfHostProxyService := user.NewSelfHostProxyService(selfHostProxyRepository)
	selfHostProxyHandler := user2.NewSelfHostProxyHandler(logger, selfHostProxyService)
	subscriptionRepository := repository.NewSubscriptionRepository(repositoryRepository)
	subscriptionService := user.NewSubscriptionService(subscriptionRepository, orderRepository)
	subscriptionHandler := user2.NewSubscriptionHandler(logger, subscriptionService)
	teamRepository := repository.NewTeamRepository(repositoryRepository)
	userSubscriptionRepository := repository.NewUserSubscriptionRepository(repositoryRepository)
	teamService := user.NewTeamService(teamRepository, userRepository, userSubscriptionRepository)
	teamHandler := user2.NewTeamHandler(logger, teamService, roleService)
	teamIPWhitelistRepository := repository.NewTeamIPWhitelistRepository(repositoryRepository)
	teamIPWhitelistService := user.NewTeamIPWhitelistService(teamIPWhitelistRepository)
	teamIPWhitelistHandler := user2.NewTeamIPWhitelistHandler(logger, teamIPWhitelistService, redisService)
	userHandler := user2.NewUserHandler(logger, userService, redisService)
	walletTransactionRepository := repository.NewWalletTransactionRepository(repositoryRepository)
	walletService := user.NewWalletService(walletTransactionRepository)
	walletHandler := user2.NewWalletHandler(logger, walletService)
	userSubscriptionService := user.NewUserSubscriptionService(userSubscriptionRepository)
	userSubscriptionHandler := user2.NewUserSubscriptionHandler(logger, userSubscriptionService)
	personalTemplateRepository := repository.NewPersonalTemplateRepository(repositoryRepository, db)
	personalTemplateService := user.NewPersonalTemplateService(personalTemplateRepository)
	personalTemplateHandler := user2.NewPersonalTemplateHandler(logger, personalTemplateService)
	onlineTemplateRepository := repository.NewOnlineTemplateRepository(repositoryRepository, db)
	onlineTemplateService := user.NewOnlineTemplateService(onlineTemplateRepository, personalTemplateRepository)
	onlineTemplateHandler := user2.NewOnlineTemplateHandler(logger, onlineTemplateService)
	ipClient := ip.NewIP2Location(viperViper, logger)
	loginHandler := auth.NewLoginHandler(logger, userService, loginLogService, teamIPWhitelistService, redisService, ipClient)
	registerHandler := auth.NewRegisterHandler(logger, userService, teamService, redisService)
	captchaService := shared.NewCaptchaService()
	captchaHandler := shared2.NewCaptchaHandler(logger, captchaService, redisService)
	smtpClient := smtp.NewSMTP(viperViper, logger)
	emailService := shared.NewEmailService(smtpClient)
	emailHandler := shared2.NewEmailHandler(logger, captchaService, redisService, emailService)
	httpServer := server.NewHTTPServer(logger, viperViper, commissionHandler, environmentHandler, groupHandler, loginLogHandler, operationLogHandler, orderHandler, roleHandler, selfHostProxyHandler, subscriptionHandler, teamHandler, teamIPWhitelistHandler, userHandler, walletHandler, userSubscriptionHandler, personalTemplateHandler, onlineTemplateHandler, loginHandler, registerHandler, captchaHandler, emailHandler)
	appApp := newApp(httpServer)
	return appApp, func() {
	}, nil
}

// wire.go:

var clientSet = wire.NewSet(oss.NewOSS, smtp.NewSMTP, ip.NewIP2Location)

var repositorySet = wire.NewSet(repository.NewDB, repository.NewRedis, repository.NewRepository, repository.NewAdminRepo, repository.NewCommissionTransactionRepository, repository.NewEnvironmentRepository, repository.NewForwardRepository, repository.NewGroupRepository, repository.NewLoginLogRepository, repository.NewOperationLogRepository, repository.NewOrderRepository, repository.NewProxyRepository, repository.NewRoleRepository, repository.NewSelfHostProxyRepository, repository.NewSubscriptionRepository, repository.NewSystemRepo, repository.NewTeamRepository, repository.NewUserBalanceRepository, repository.NewUserRepository, repository.NewUserSubscriptionRepository, repository.NewWalletTransactionRepository, repository.NewTeamIPWhitelistRepository, repository.NewPersonalTemplateRepository, repository.NewOnlineTemplateRepository)

var serviceSet = wire.NewSet(user.NewService, user.NewCommissionService, user.NewEnvironmentService, user.NewGroupService, user.NewLoginLogService, user.NewOperationLogService, user.NewOrderService, user.NewProxyService, user.NewRoleService, user.NewSelfHostProxyService, user.NewSubscriptionService, user.NewTeamService, user.NewUserService, user.NewUserSubscriptionService, user.NewWalletService, user.NewTeamIPWhitelistService, user.NewPersonalTemplateService, user.NewOnlineTemplateService, shared.NewRedisService, shared.NewOSSService, shared.NewCaptchaService, shared.NewEmailService)

var handlerSet = wire.NewSet(user2.NewHandler, user2.NewCommissionHandler, user2.NewEnvironmentHandler, user2.NewGroupHandler, user2.NewLoginLogHandler, user2.NewOperationLogHandler, user2.NewOrderHandler, user2.NewRoleHandler, user2.NewSelfHostProxyHandler, user2.NewSubscriptionHandler, user2.NewTeamHandler, user2.NewTeamIPWhitelistHandler, user2.NewUserHandler, user2.NewWalletHandler, user2.NewUserSubscriptionHandler, user2.NewPersonalTemplateHandler, user2.NewOnlineTemplateHandler, auth.NewHandler, auth.NewLoginHandler, auth.NewRegisterHandler, shared2.NewHandler, shared2.NewCaptchaHandler, shared2.NewEmailHandler)

var serverSet = wire.NewSet(server.NewHTTPServer)

// build App
func newApp(
	httpServer *http.Server,
) *app.App {
	return app.NewApp(app.WithServer(httpServer), app.WithName("suiyu-server"))
}
