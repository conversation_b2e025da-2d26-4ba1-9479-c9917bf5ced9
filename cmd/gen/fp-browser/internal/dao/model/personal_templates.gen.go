// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNamePersonalTemplate = "personal_templates"

// PersonalTemplate mapped from table <personal_templates>
type PersonalTemplate struct {
	ID        int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true" json:"id"`
	Name      string     `gorm:"column:name;type:character varying(100);not null;index:idx_personal_templates_name,priority:1;comment:模板名称" json:"name"` // 模板名称
	Template  string     `gorm:"column:template;type:text;not null;comment:YAML配置文件内容，以字符串形式存储" json:"template"`                                         // YAML配置文件内容，以字符串形式存储
	CreatedAt *time.Time `gorm:"column:created_at;type:timestamp with time zone;index:idx_personal_templates_created_at,priority:1;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt *time.Time `gorm:"column:updated_at;type:timestamp with time zone;default:CURRENT_TIMESTAMP" json:"updated_at"`
	Tags      *string    `gorm:"column:tags;type:text[];index:idx_personal_templates_tags,priority:1;default:{};comment:模板标签数组" json:"tags"`           // 模板标签数组
	UserID    int32      `gorm:"column:user_id;type:integer;not null;index:idx_personal_templates_user_id,priority:1;comment:模板所属用户ID" json:"user_id"` // 模板所属用户ID
	IsDeleted *bool      `gorm:"column:is_deleted;type:boolean" json:"is_deleted"`
}

// TableName PersonalTemplate's table name
func (*PersonalTemplate) TableName() string {
	return TableNamePersonalTemplate
}
