// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameGroup = "groups"

// Group mapped from table <groups>
type Group struct {
	ID        int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true;comment:分组主键，自增 ID" json:"id"`                                        // 分组主键，自增 ID
	Name      string     `gorm:"column:name;type:character varying(100);not null;comment:分组名称，最长 100 字符" json:"name"`                                      // 分组名称，最长 100 字符
	UserID    int32      `gorm:"column:user_id;type:integer;not null;index:idx_group_user_id,priority:1;comment:分组所属用户 ID" json:"user_id"`                 // 分组所属用户 ID
	TeamID    int32      `gorm:"column:team_id;type:integer;not null;index:idx_group_team_id,priority:1;comment:分组所属团队 ID" json:"team_id"`                 // 分组所属团队 ID
	CreatedAt *time.Time `gorm:"column:created_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:记录创建时间" json:"created_at"`   // 记录创建时间
	UpdatedAt *time.Time `gorm:"column:updated_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:记录最近更新时间" json:"updated_at"` // 记录最近更新时间
	Comment   *string    `gorm:"column:comment;type:character varying(100);comment:备注内容" json:"comment"`                                                   // 备注内容
}

// TableName Group's table name
func (*Group) TableName() string {
	return TableNameGroup
}
