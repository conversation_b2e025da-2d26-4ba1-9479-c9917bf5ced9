// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameCoupon = "coupons"

// Coupon mapped from table <coupons>
type Coupon struct {
	ID                   int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true;comment:自增主键ID" json:"id"`                                        // 自增主键ID
	CouponCode           string     `gorm:"column:coupon_code;type:text;not null;comment:优惠券唯一ID（字符串），供用户使用输入" json:"coupon_code"`                                // 优惠券唯一ID（字符串），供用户使用输入
	Name                 string     `gorm:"column:name;type:text;not null;comment:优惠券名称，展示给用户" json:"name"`                                                       // 优惠券名称，展示给用户
	Type                 int16      `gorm:"column:type;type:smallint;not null;comment:优惠券类型：0 表示固定金额（fixed），1 表示百分比折扣（percent）" json:"type"`                      // 优惠券类型：0 表示固定金额（fixed），1 表示百分比折扣（percent）
	Value                int32      `gorm:"column:value;type:integer;not null;comment:优惠额度：单位为分（如100 = 1元）或百分比（如10 = 10%）" json:"value"`                          // 优惠额度：单位为分（如100 = 1元）或百分比（如10 = 10%）
	MaxUsage             int32      `gorm:"column:max_usage;type:integer;not null;comment:优惠券最大使用总次数" json:"max_usage"`                                           // 优惠券最大使用总次数
	MaxUsagePerUser      int32      `gorm:"column:max_usage_per_user;type:integer;not null;comment:每个用户最多使用次数" json:"max_usage_per_user"`                         // 每个用户最多使用次数
	TargetSubscriptionID *int32     `gorm:"column:target_subscription_id;type:integer;comment:绑定的套餐ID，空则表示所有套餐均可使用" json:"target_subscription_id"`                // 绑定的套餐ID，空则表示所有套餐均可使用
	PromoterID           *int32     `gorm:"column:promoter_id;type:integer;comment:优惠券关联的推广者ID，可为空" json:"promoter_id"`                                           // 优惠券关联的推广者ID，可为空
	AutoBindPromoter     bool       `gorm:"column:auto_bind_promoter;type:boolean;not null;comment:是否在用户使用该券时自动绑定其推广关系" json:"auto_bind_promoter"`                // 是否在用户使用该券时自动绑定其推广关系
	CreatedAt            *time.Time `gorm:"column:created_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	ItemType             *int16     `gorm:"column:item_type;type:smallint;not null;default:1;comment:1为套餐.2为代理" json:"item_type"`                                 // 1为套餐.2为代理
}

// TableName Coupon's table name
func (*Coupon) TableName() string {
	return TableNameCoupon
}
