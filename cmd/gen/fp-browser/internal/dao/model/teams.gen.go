// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTeam = "teams"

// Team mapped from table <teams>
type Team struct {
	ID        int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true;comment:团队 ID，自增主键" json:"id"`                                        // 团队 ID，自增主键
	Name      string     `gorm:"column:name;type:character varying(50);not null;comment:团队名称，最长 50 字符" json:"name"`                                        // 团队名称，最长 50 字符
	OwnerID   int32      `gorm:"column:owner_id;type:integer;not null;index:idx_team_owner_id,priority:1;comment:团队创建者的用户 ID" json:"owner_id"`             // 团队创建者的用户 ID
	CreatedAt *time.Time `gorm:"column:created_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:团队创建时间" json:"created_at"`   // 团队创建时间
	UpdatedAt *time.Time `gorm:"column:updated_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:团队最后更新时间" json:"updated_at"` // 团队最后更新时间
}

// TableName Team's table name
func (*Team) TableName() string {
	return TableNameTeam
}
