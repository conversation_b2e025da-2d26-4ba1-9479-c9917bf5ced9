// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"fp-browser/cmd/gen/internal/dao/model"
)

func newTeamIPWhitelist(db *gorm.DB, opts ...gen.DOOption) teamIPWhitelist {
	_teamIPWhitelist := teamIPWhitelist{}

	_teamIPWhitelist.teamIPWhitelistDo.UseDB(db, opts...)
	_teamIPWhitelist.teamIPWhitelistDo.UseModel(&model.TeamIPWhitelist{})

	tableName := _teamIPWhitelist.teamIPWhitelistDo.TableName()
	_teamIPWhitelist.ALL = field.NewAsterisk(tableName)
	_teamIPWhitelist.ID = field.NewInt32(tableName, "id")
	_teamIPWhitelist.TeamID = field.NewInt32(tableName, "team_id")
	_teamIPWhitelist.IPAddress = field.NewString(tableName, "ip_address")
	_teamIPWhitelist.CreatedAt = field.NewTime(tableName, "created_at")

	_teamIPWhitelist.fillFieldMap()

	return _teamIPWhitelist
}

type teamIPWhitelist struct {
	teamIPWhitelistDo

	ALL       field.Asterisk
	ID        field.Int32
	TeamID    field.Int32  // 关联的团队 ID
	IPAddress field.String // 允许登录的 IP 地址
	CreatedAt field.Time   // 创建时间

	fieldMap map[string]field.Expr
}

func (t teamIPWhitelist) Table(newTableName string) *teamIPWhitelist {
	t.teamIPWhitelistDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t teamIPWhitelist) As(alias string) *teamIPWhitelist {
	t.teamIPWhitelistDo.DO = *(t.teamIPWhitelistDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *teamIPWhitelist) updateTableName(table string) *teamIPWhitelist {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewInt32(table, "id")
	t.TeamID = field.NewInt32(table, "team_id")
	t.IPAddress = field.NewString(table, "ip_address")
	t.CreatedAt = field.NewTime(table, "created_at")

	t.fillFieldMap()

	return t
}

func (t *teamIPWhitelist) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *teamIPWhitelist) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 4)
	t.fieldMap["id"] = t.ID
	t.fieldMap["team_id"] = t.TeamID
	t.fieldMap["ip_address"] = t.IPAddress
	t.fieldMap["created_at"] = t.CreatedAt
}

func (t teamIPWhitelist) clone(db *gorm.DB) teamIPWhitelist {
	t.teamIPWhitelistDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t teamIPWhitelist) replaceDB(db *gorm.DB) teamIPWhitelist {
	t.teamIPWhitelistDo.ReplaceDB(db)
	return t
}

type teamIPWhitelistDo struct{ gen.DO }

type ITeamIPWhitelistDo interface {
	gen.SubQuery
	Debug() ITeamIPWhitelistDo
	WithContext(ctx context.Context) ITeamIPWhitelistDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ITeamIPWhitelistDo
	WriteDB() ITeamIPWhitelistDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ITeamIPWhitelistDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ITeamIPWhitelistDo
	Not(conds ...gen.Condition) ITeamIPWhitelistDo
	Or(conds ...gen.Condition) ITeamIPWhitelistDo
	Select(conds ...field.Expr) ITeamIPWhitelistDo
	Where(conds ...gen.Condition) ITeamIPWhitelistDo
	Order(conds ...field.Expr) ITeamIPWhitelistDo
	Distinct(cols ...field.Expr) ITeamIPWhitelistDo
	Omit(cols ...field.Expr) ITeamIPWhitelistDo
	Join(table schema.Tabler, on ...field.Expr) ITeamIPWhitelistDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ITeamIPWhitelistDo
	RightJoin(table schema.Tabler, on ...field.Expr) ITeamIPWhitelistDo
	Group(cols ...field.Expr) ITeamIPWhitelistDo
	Having(conds ...gen.Condition) ITeamIPWhitelistDo
	Limit(limit int) ITeamIPWhitelistDo
	Offset(offset int) ITeamIPWhitelistDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ITeamIPWhitelistDo
	Unscoped() ITeamIPWhitelistDo
	Create(values ...*model.TeamIPWhitelist) error
	CreateInBatches(values []*model.TeamIPWhitelist, batchSize int) error
	Save(values ...*model.TeamIPWhitelist) error
	First() (*model.TeamIPWhitelist, error)
	Take() (*model.TeamIPWhitelist, error)
	Last() (*model.TeamIPWhitelist, error)
	Find() ([]*model.TeamIPWhitelist, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TeamIPWhitelist, err error)
	FindInBatches(result *[]*model.TeamIPWhitelist, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.TeamIPWhitelist) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ITeamIPWhitelistDo
	Assign(attrs ...field.AssignExpr) ITeamIPWhitelistDo
	Joins(fields ...field.RelationField) ITeamIPWhitelistDo
	Preload(fields ...field.RelationField) ITeamIPWhitelistDo
	FirstOrInit() (*model.TeamIPWhitelist, error)
	FirstOrCreate() (*model.TeamIPWhitelist, error)
	FindByPage(offset int, limit int) (result []*model.TeamIPWhitelist, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ITeamIPWhitelistDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t teamIPWhitelistDo) Debug() ITeamIPWhitelistDo {
	return t.withDO(t.DO.Debug())
}

func (t teamIPWhitelistDo) WithContext(ctx context.Context) ITeamIPWhitelistDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t teamIPWhitelistDo) ReadDB() ITeamIPWhitelistDo {
	return t.Clauses(dbresolver.Read)
}

func (t teamIPWhitelistDo) WriteDB() ITeamIPWhitelistDo {
	return t.Clauses(dbresolver.Write)
}

func (t teamIPWhitelistDo) Session(config *gorm.Session) ITeamIPWhitelistDo {
	return t.withDO(t.DO.Session(config))
}

func (t teamIPWhitelistDo) Clauses(conds ...clause.Expression) ITeamIPWhitelistDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t teamIPWhitelistDo) Returning(value interface{}, columns ...string) ITeamIPWhitelistDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t teamIPWhitelistDo) Not(conds ...gen.Condition) ITeamIPWhitelistDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t teamIPWhitelistDo) Or(conds ...gen.Condition) ITeamIPWhitelistDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t teamIPWhitelistDo) Select(conds ...field.Expr) ITeamIPWhitelistDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t teamIPWhitelistDo) Where(conds ...gen.Condition) ITeamIPWhitelistDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t teamIPWhitelistDo) Order(conds ...field.Expr) ITeamIPWhitelistDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t teamIPWhitelistDo) Distinct(cols ...field.Expr) ITeamIPWhitelistDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t teamIPWhitelistDo) Omit(cols ...field.Expr) ITeamIPWhitelistDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t teamIPWhitelistDo) Join(table schema.Tabler, on ...field.Expr) ITeamIPWhitelistDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t teamIPWhitelistDo) LeftJoin(table schema.Tabler, on ...field.Expr) ITeamIPWhitelistDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t teamIPWhitelistDo) RightJoin(table schema.Tabler, on ...field.Expr) ITeamIPWhitelistDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t teamIPWhitelistDo) Group(cols ...field.Expr) ITeamIPWhitelistDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t teamIPWhitelistDo) Having(conds ...gen.Condition) ITeamIPWhitelistDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t teamIPWhitelistDo) Limit(limit int) ITeamIPWhitelistDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t teamIPWhitelistDo) Offset(offset int) ITeamIPWhitelistDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t teamIPWhitelistDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ITeamIPWhitelistDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t teamIPWhitelistDo) Unscoped() ITeamIPWhitelistDo {
	return t.withDO(t.DO.Unscoped())
}

func (t teamIPWhitelistDo) Create(values ...*model.TeamIPWhitelist) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t teamIPWhitelistDo) CreateInBatches(values []*model.TeamIPWhitelist, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t teamIPWhitelistDo) Save(values ...*model.TeamIPWhitelist) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t teamIPWhitelistDo) First() (*model.TeamIPWhitelist, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TeamIPWhitelist), nil
	}
}

func (t teamIPWhitelistDo) Take() (*model.TeamIPWhitelist, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TeamIPWhitelist), nil
	}
}

func (t teamIPWhitelistDo) Last() (*model.TeamIPWhitelist, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TeamIPWhitelist), nil
	}
}

func (t teamIPWhitelistDo) Find() ([]*model.TeamIPWhitelist, error) {
	result, err := t.DO.Find()
	return result.([]*model.TeamIPWhitelist), err
}

func (t teamIPWhitelistDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TeamIPWhitelist, err error) {
	buf := make([]*model.TeamIPWhitelist, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t teamIPWhitelistDo) FindInBatches(result *[]*model.TeamIPWhitelist, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t teamIPWhitelistDo) Attrs(attrs ...field.AssignExpr) ITeamIPWhitelistDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t teamIPWhitelistDo) Assign(attrs ...field.AssignExpr) ITeamIPWhitelistDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t teamIPWhitelistDo) Joins(fields ...field.RelationField) ITeamIPWhitelistDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t teamIPWhitelistDo) Preload(fields ...field.RelationField) ITeamIPWhitelistDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t teamIPWhitelistDo) FirstOrInit() (*model.TeamIPWhitelist, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TeamIPWhitelist), nil
	}
}

func (t teamIPWhitelistDo) FirstOrCreate() (*model.TeamIPWhitelist, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TeamIPWhitelist), nil
	}
}

func (t teamIPWhitelistDo) FindByPage(offset int, limit int) (result []*model.TeamIPWhitelist, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t teamIPWhitelistDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t teamIPWhitelistDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t teamIPWhitelistDo) Delete(models ...*model.TeamIPWhitelist) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *teamIPWhitelistDo) withDO(do gen.Dao) *teamIPWhitelistDo {
	t.DO = *do.(*gen.DO)
	return t
}
