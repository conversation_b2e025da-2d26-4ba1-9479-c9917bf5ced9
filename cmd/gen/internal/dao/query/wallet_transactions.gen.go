// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"fp-browser/cmd/gen/internal/dao/model"
)

func newWalletTransaction(db *gorm.DB, opts ...gen.DOOption) walletTransaction {
	_walletTransaction := walletTransaction{}

	_walletTransaction.walletTransactionDo.UseDB(db, opts...)
	_walletTransaction.walletTransactionDo.UseModel(&model.WalletTransaction{})

	tableName := _walletTransaction.walletTransactionDo.TableName()
	_walletTransaction.ALL = field.NewAsterisk(tableName)
	_walletTransaction.ID = field.NewInt32(tableName, "id")
	_walletTransaction.UserID = field.NewInt32(tableName, "user_id")
	_walletTransaction.Amount = field.NewInt64(tableName, "amount")
	_walletTransaction.Currency = field.NewString(tableName, "currency")
	_walletTransaction.TransactionType = field.NewInt16(tableName, "transaction_type")
	_walletTransaction.ReferenceID = field.NewInt32(tableName, "reference_id")
	_walletTransaction.Description = field.NewString(tableName, "description")
	_walletTransaction.CreatedAt = field.NewTime(tableName, "created_at")

	_walletTransaction.fillFieldMap()

	return _walletTransaction
}

type walletTransaction struct {
	walletTransactionDo

	ALL             field.Asterisk
	ID              field.Int32  // 主键，自增交易 ID
	UserID          field.Int32  // 用户 ID
	Amount          field.Int64  // 交易金额，单位为分，正数为收入，负数为支出
	Currency        field.String // 货币类型，ISO 4217 编码（如 CNY, USD）
	TransactionType field.Int16  // 交易类型（1充值，2消费，3退款等）
	ReferenceID     field.Int32  // 相关订单或对象的引用 ID，默认 0 表示无
	Description     field.String // 交易说明，最长 100 字符
	CreatedAt       field.Time   // 交易记录创建时间

	fieldMap map[string]field.Expr
}

func (w walletTransaction) Table(newTableName string) *walletTransaction {
	w.walletTransactionDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w walletTransaction) As(alias string) *walletTransaction {
	w.walletTransactionDo.DO = *(w.walletTransactionDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *walletTransaction) updateTableName(table string) *walletTransaction {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewInt32(table, "id")
	w.UserID = field.NewInt32(table, "user_id")
	w.Amount = field.NewInt64(table, "amount")
	w.Currency = field.NewString(table, "currency")
	w.TransactionType = field.NewInt16(table, "transaction_type")
	w.ReferenceID = field.NewInt32(table, "reference_id")
	w.Description = field.NewString(table, "description")
	w.CreatedAt = field.NewTime(table, "created_at")

	w.fillFieldMap()

	return w
}

func (w *walletTransaction) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *walletTransaction) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 8)
	w.fieldMap["id"] = w.ID
	w.fieldMap["user_id"] = w.UserID
	w.fieldMap["amount"] = w.Amount
	w.fieldMap["currency"] = w.Currency
	w.fieldMap["transaction_type"] = w.TransactionType
	w.fieldMap["reference_id"] = w.ReferenceID
	w.fieldMap["description"] = w.Description
	w.fieldMap["created_at"] = w.CreatedAt
}

func (w walletTransaction) clone(db *gorm.DB) walletTransaction {
	w.walletTransactionDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w walletTransaction) replaceDB(db *gorm.DB) walletTransaction {
	w.walletTransactionDo.ReplaceDB(db)
	return w
}

type walletTransactionDo struct{ gen.DO }

type IWalletTransactionDo interface {
	gen.SubQuery
	Debug() IWalletTransactionDo
	WithContext(ctx context.Context) IWalletTransactionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IWalletTransactionDo
	WriteDB() IWalletTransactionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IWalletTransactionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IWalletTransactionDo
	Not(conds ...gen.Condition) IWalletTransactionDo
	Or(conds ...gen.Condition) IWalletTransactionDo
	Select(conds ...field.Expr) IWalletTransactionDo
	Where(conds ...gen.Condition) IWalletTransactionDo
	Order(conds ...field.Expr) IWalletTransactionDo
	Distinct(cols ...field.Expr) IWalletTransactionDo
	Omit(cols ...field.Expr) IWalletTransactionDo
	Join(table schema.Tabler, on ...field.Expr) IWalletTransactionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IWalletTransactionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IWalletTransactionDo
	Group(cols ...field.Expr) IWalletTransactionDo
	Having(conds ...gen.Condition) IWalletTransactionDo
	Limit(limit int) IWalletTransactionDo
	Offset(offset int) IWalletTransactionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IWalletTransactionDo
	Unscoped() IWalletTransactionDo
	Create(values ...*model.WalletTransaction) error
	CreateInBatches(values []*model.WalletTransaction, batchSize int) error
	Save(values ...*model.WalletTransaction) error
	First() (*model.WalletTransaction, error)
	Take() (*model.WalletTransaction, error)
	Last() (*model.WalletTransaction, error)
	Find() ([]*model.WalletTransaction, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.WalletTransaction, err error)
	FindInBatches(result *[]*model.WalletTransaction, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.WalletTransaction) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IWalletTransactionDo
	Assign(attrs ...field.AssignExpr) IWalletTransactionDo
	Joins(fields ...field.RelationField) IWalletTransactionDo
	Preload(fields ...field.RelationField) IWalletTransactionDo
	FirstOrInit() (*model.WalletTransaction, error)
	FirstOrCreate() (*model.WalletTransaction, error)
	FindByPage(offset int, limit int) (result []*model.WalletTransaction, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IWalletTransactionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (w walletTransactionDo) Debug() IWalletTransactionDo {
	return w.withDO(w.DO.Debug())
}

func (w walletTransactionDo) WithContext(ctx context.Context) IWalletTransactionDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w walletTransactionDo) ReadDB() IWalletTransactionDo {
	return w.Clauses(dbresolver.Read)
}

func (w walletTransactionDo) WriteDB() IWalletTransactionDo {
	return w.Clauses(dbresolver.Write)
}

func (w walletTransactionDo) Session(config *gorm.Session) IWalletTransactionDo {
	return w.withDO(w.DO.Session(config))
}

func (w walletTransactionDo) Clauses(conds ...clause.Expression) IWalletTransactionDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w walletTransactionDo) Returning(value interface{}, columns ...string) IWalletTransactionDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w walletTransactionDo) Not(conds ...gen.Condition) IWalletTransactionDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w walletTransactionDo) Or(conds ...gen.Condition) IWalletTransactionDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w walletTransactionDo) Select(conds ...field.Expr) IWalletTransactionDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w walletTransactionDo) Where(conds ...gen.Condition) IWalletTransactionDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w walletTransactionDo) Order(conds ...field.Expr) IWalletTransactionDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w walletTransactionDo) Distinct(cols ...field.Expr) IWalletTransactionDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w walletTransactionDo) Omit(cols ...field.Expr) IWalletTransactionDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w walletTransactionDo) Join(table schema.Tabler, on ...field.Expr) IWalletTransactionDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w walletTransactionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IWalletTransactionDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w walletTransactionDo) RightJoin(table schema.Tabler, on ...field.Expr) IWalletTransactionDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w walletTransactionDo) Group(cols ...field.Expr) IWalletTransactionDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w walletTransactionDo) Having(conds ...gen.Condition) IWalletTransactionDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w walletTransactionDo) Limit(limit int) IWalletTransactionDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w walletTransactionDo) Offset(offset int) IWalletTransactionDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w walletTransactionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IWalletTransactionDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w walletTransactionDo) Unscoped() IWalletTransactionDo {
	return w.withDO(w.DO.Unscoped())
}

func (w walletTransactionDo) Create(values ...*model.WalletTransaction) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w walletTransactionDo) CreateInBatches(values []*model.WalletTransaction, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w walletTransactionDo) Save(values ...*model.WalletTransaction) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w walletTransactionDo) First() (*model.WalletTransaction, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.WalletTransaction), nil
	}
}

func (w walletTransactionDo) Take() (*model.WalletTransaction, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.WalletTransaction), nil
	}
}

func (w walletTransactionDo) Last() (*model.WalletTransaction, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.WalletTransaction), nil
	}
}

func (w walletTransactionDo) Find() ([]*model.WalletTransaction, error) {
	result, err := w.DO.Find()
	return result.([]*model.WalletTransaction), err
}

func (w walletTransactionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.WalletTransaction, err error) {
	buf := make([]*model.WalletTransaction, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w walletTransactionDo) FindInBatches(result *[]*model.WalletTransaction, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w walletTransactionDo) Attrs(attrs ...field.AssignExpr) IWalletTransactionDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w walletTransactionDo) Assign(attrs ...field.AssignExpr) IWalletTransactionDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w walletTransactionDo) Joins(fields ...field.RelationField) IWalletTransactionDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w walletTransactionDo) Preload(fields ...field.RelationField) IWalletTransactionDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w walletTransactionDo) FirstOrInit() (*model.WalletTransaction, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.WalletTransaction), nil
	}
}

func (w walletTransactionDo) FirstOrCreate() (*model.WalletTransaction, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.WalletTransaction), nil
	}
}

func (w walletTransactionDo) FindByPage(offset int, limit int) (result []*model.WalletTransaction, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w walletTransactionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w walletTransactionDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w walletTransactionDo) Delete(models ...*model.WalletTransaction) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *walletTransactionDo) withDO(do gen.Dao) *walletTransactionDo {
	w.DO = *do.(*gen.DO)
	return w
}
