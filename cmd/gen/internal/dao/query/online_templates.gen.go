// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"fp-browser/internal/dao/model"
)

func newOnlineTemplate(db *gorm.DB, opts ...gen.DOOption) onlineTemplate {
	_onlineTemplate := onlineTemplate{}

	_onlineTemplate.onlineTemplateDo.UseDB(db, opts...)
	_onlineTemplate.onlineTemplateDo.UseModel(&model.OnlineTemplate{})

	tableName := _onlineTemplate.onlineTemplateDo.TableName()
	_onlineTemplate.ALL = field.NewAsterisk(tableName)
	_onlineTemplate.ID = field.NewInt32(tableName, "id")
	_onlineTemplate.Name = field.NewString(tableName, "name")
	_onlineTemplate.Template = field.NewString(tableName, "template")
	_onlineTemplate.CreatedAt = field.NewTime(tableName, "created_at")
	_onlineTemplate.UpdatedAt = field.NewTime(tableName, "updated_at")
	_onlineTemplate.Tags = field.NewString(tableName, "tags")
	_onlineTemplate.CreatedBy = field.NewInt32(tableName, "created_by")
	_onlineTemplate.DownloadCount = field.NewInt32(tableName, "download_count")
	_onlineTemplate.IsPublic = field.NewBool(tableName, "is_public")
	_onlineTemplate.IsFeatured = field.NewBool(tableName, "is_featured")
	_onlineTemplate.IsDeleted = field.NewBool(tableName, "is_deleted")

	_onlineTemplate.fillFieldMap()

	return _onlineTemplate
}

type onlineTemplate struct {
	onlineTemplateDo

	ALL           field.Asterisk
	ID            field.Int32
	Name          field.String // 模板名称
	Template      field.String // YAML配置文件内容，以字符串形式存储
	CreatedAt     field.Time
	UpdatedAt     field.Time
	Tags          field.String // 模板标签数组
	CreatedBy     field.Int32  // 模板创建者用户ID
	DownloadCount field.Int32  // 模板下载次数
	IsPublic      field.Bool   // 是否为公开模板
	IsFeatured    field.Bool   // 是否为精选模板
	IsDeleted     field.Bool

	fieldMap map[string]field.Expr
}

func (o onlineTemplate) Table(newTableName string) *onlineTemplate {
	o.onlineTemplateDo.UseTable(newTableName)
	return o.updateTableName(newTableName)
}

func (o onlineTemplate) As(alias string) *onlineTemplate {
	o.onlineTemplateDo.DO = *(o.onlineTemplateDo.As(alias).(*gen.DO))
	return o.updateTableName(alias)
}

func (o *onlineTemplate) updateTableName(table string) *onlineTemplate {
	o.ALL = field.NewAsterisk(table)
	o.ID = field.NewInt32(table, "id")
	o.Name = field.NewString(table, "name")
	o.Template = field.NewString(table, "template")
	o.CreatedAt = field.NewTime(table, "created_at")
	o.UpdatedAt = field.NewTime(table, "updated_at")
	o.Tags = field.NewString(table, "tags")
	o.CreatedBy = field.NewInt32(table, "created_by")
	o.DownloadCount = field.NewInt32(table, "download_count")
	o.IsPublic = field.NewBool(table, "is_public")
	o.IsFeatured = field.NewBool(table, "is_featured")
	o.IsDeleted = field.NewBool(table, "is_deleted")

	o.fillFieldMap()

	return o
}

func (o *onlineTemplate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := o.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (o *onlineTemplate) fillFieldMap() {
	o.fieldMap = make(map[string]field.Expr, 11)
	o.fieldMap["id"] = o.ID
	o.fieldMap["name"] = o.Name
	o.fieldMap["template"] = o.Template
	o.fieldMap["created_at"] = o.CreatedAt
	o.fieldMap["updated_at"] = o.UpdatedAt
	o.fieldMap["tags"] = o.Tags
	o.fieldMap["created_by"] = o.CreatedBy
	o.fieldMap["download_count"] = o.DownloadCount
	o.fieldMap["is_public"] = o.IsPublic
	o.fieldMap["is_featured"] = o.IsFeatured
	o.fieldMap["is_deleted"] = o.IsDeleted
}

func (o onlineTemplate) clone(db *gorm.DB) onlineTemplate {
	o.onlineTemplateDo.ReplaceConnPool(db.Statement.ConnPool)
	return o
}

func (o onlineTemplate) replaceDB(db *gorm.DB) onlineTemplate {
	o.onlineTemplateDo.ReplaceDB(db)
	return o
}

type onlineTemplateDo struct{ gen.DO }

type IOnlineTemplateDo interface {
	gen.SubQuery
	Debug() IOnlineTemplateDo
	WithContext(ctx context.Context) IOnlineTemplateDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IOnlineTemplateDo
	WriteDB() IOnlineTemplateDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IOnlineTemplateDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IOnlineTemplateDo
	Not(conds ...gen.Condition) IOnlineTemplateDo
	Or(conds ...gen.Condition) IOnlineTemplateDo
	Select(conds ...field.Expr) IOnlineTemplateDo
	Where(conds ...gen.Condition) IOnlineTemplateDo
	Order(conds ...field.Expr) IOnlineTemplateDo
	Distinct(cols ...field.Expr) IOnlineTemplateDo
	Omit(cols ...field.Expr) IOnlineTemplateDo
	Join(table schema.Tabler, on ...field.Expr) IOnlineTemplateDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IOnlineTemplateDo
	RightJoin(table schema.Tabler, on ...field.Expr) IOnlineTemplateDo
	Group(cols ...field.Expr) IOnlineTemplateDo
	Having(conds ...gen.Condition) IOnlineTemplateDo
	Limit(limit int) IOnlineTemplateDo
	Offset(offset int) IOnlineTemplateDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IOnlineTemplateDo
	Unscoped() IOnlineTemplateDo
	Create(values ...*model.OnlineTemplate) error
	CreateInBatches(values []*model.OnlineTemplate, batchSize int) error
	Save(values ...*model.OnlineTemplate) error
	First() (*model.OnlineTemplate, error)
	Take() (*model.OnlineTemplate, error)
	Last() (*model.OnlineTemplate, error)
	Find() ([]*model.OnlineTemplate, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.OnlineTemplate, err error)
	FindInBatches(result *[]*model.OnlineTemplate, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.OnlineTemplate) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IOnlineTemplateDo
	Assign(attrs ...field.AssignExpr) IOnlineTemplateDo
	Joins(fields ...field.RelationField) IOnlineTemplateDo
	Preload(fields ...field.RelationField) IOnlineTemplateDo
	FirstOrInit() (*model.OnlineTemplate, error)
	FirstOrCreate() (*model.OnlineTemplate, error)
	FindByPage(offset int, limit int) (result []*model.OnlineTemplate, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IOnlineTemplateDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (o onlineTemplateDo) Debug() IOnlineTemplateDo {
	return o.withDO(o.DO.Debug())
}

func (o onlineTemplateDo) WithContext(ctx context.Context) IOnlineTemplateDo {
	return o.withDO(o.DO.WithContext(ctx))
}

func (o onlineTemplateDo) ReadDB() IOnlineTemplateDo {
	return o.Clauses(dbresolver.Read)
}

func (o onlineTemplateDo) WriteDB() IOnlineTemplateDo {
	return o.Clauses(dbresolver.Write)
}

func (o onlineTemplateDo) Session(config *gorm.Session) IOnlineTemplateDo {
	return o.withDO(o.DO.Session(config))
}

func (o onlineTemplateDo) Clauses(conds ...clause.Expression) IOnlineTemplateDo {
	return o.withDO(o.DO.Clauses(conds...))
}

func (o onlineTemplateDo) Returning(value interface{}, columns ...string) IOnlineTemplateDo {
	return o.withDO(o.DO.Returning(value, columns...))
}

func (o onlineTemplateDo) Not(conds ...gen.Condition) IOnlineTemplateDo {
	return o.withDO(o.DO.Not(conds...))
}

func (o onlineTemplateDo) Or(conds ...gen.Condition) IOnlineTemplateDo {
	return o.withDO(o.DO.Or(conds...))
}

func (o onlineTemplateDo) Select(conds ...field.Expr) IOnlineTemplateDo {
	return o.withDO(o.DO.Select(conds...))
}

func (o onlineTemplateDo) Where(conds ...gen.Condition) IOnlineTemplateDo {
	return o.withDO(o.DO.Where(conds...))
}

func (o onlineTemplateDo) Order(conds ...field.Expr) IOnlineTemplateDo {
	return o.withDO(o.DO.Order(conds...))
}

func (o onlineTemplateDo) Distinct(cols ...field.Expr) IOnlineTemplateDo {
	return o.withDO(o.DO.Distinct(cols...))
}

func (o onlineTemplateDo) Omit(cols ...field.Expr) IOnlineTemplateDo {
	return o.withDO(o.DO.Omit(cols...))
}

func (o onlineTemplateDo) Join(table schema.Tabler, on ...field.Expr) IOnlineTemplateDo {
	return o.withDO(o.DO.Join(table, on...))
}

func (o onlineTemplateDo) LeftJoin(table schema.Tabler, on ...field.Expr) IOnlineTemplateDo {
	return o.withDO(o.DO.LeftJoin(table, on...))
}

func (o onlineTemplateDo) RightJoin(table schema.Tabler, on ...field.Expr) IOnlineTemplateDo {
	return o.withDO(o.DO.RightJoin(table, on...))
}

func (o onlineTemplateDo) Group(cols ...field.Expr) IOnlineTemplateDo {
	return o.withDO(o.DO.Group(cols...))
}

func (o onlineTemplateDo) Having(conds ...gen.Condition) IOnlineTemplateDo {
	return o.withDO(o.DO.Having(conds...))
}

func (o onlineTemplateDo) Limit(limit int) IOnlineTemplateDo {
	return o.withDO(o.DO.Limit(limit))
}

func (o onlineTemplateDo) Offset(offset int) IOnlineTemplateDo {
	return o.withDO(o.DO.Offset(offset))
}

func (o onlineTemplateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IOnlineTemplateDo {
	return o.withDO(o.DO.Scopes(funcs...))
}

func (o onlineTemplateDo) Unscoped() IOnlineTemplateDo {
	return o.withDO(o.DO.Unscoped())
}

func (o onlineTemplateDo) Create(values ...*model.OnlineTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Create(values)
}

func (o onlineTemplateDo) CreateInBatches(values []*model.OnlineTemplate, batchSize int) error {
	return o.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (o onlineTemplateDo) Save(values ...*model.OnlineTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Save(values)
}

func (o onlineTemplateDo) First() (*model.OnlineTemplate, error) {
	if result, err := o.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.OnlineTemplate), nil
	}
}

func (o onlineTemplateDo) Take() (*model.OnlineTemplate, error) {
	if result, err := o.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.OnlineTemplate), nil
	}
}

func (o onlineTemplateDo) Last() (*model.OnlineTemplate, error) {
	if result, err := o.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.OnlineTemplate), nil
	}
}

func (o onlineTemplateDo) Find() ([]*model.OnlineTemplate, error) {
	result, err := o.DO.Find()
	return result.([]*model.OnlineTemplate), err
}

func (o onlineTemplateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.OnlineTemplate, err error) {
	buf := make([]*model.OnlineTemplate, 0, batchSize)
	err = o.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (o onlineTemplateDo) FindInBatches(result *[]*model.OnlineTemplate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return o.DO.FindInBatches(result, batchSize, fc)
}

func (o onlineTemplateDo) Attrs(attrs ...field.AssignExpr) IOnlineTemplateDo {
	return o.withDO(o.DO.Attrs(attrs...))
}

func (o onlineTemplateDo) Assign(attrs ...field.AssignExpr) IOnlineTemplateDo {
	return o.withDO(o.DO.Assign(attrs...))
}

func (o onlineTemplateDo) Joins(fields ...field.RelationField) IOnlineTemplateDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Joins(_f))
	}
	return &o
}

func (o onlineTemplateDo) Preload(fields ...field.RelationField) IOnlineTemplateDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Preload(_f))
	}
	return &o
}

func (o onlineTemplateDo) FirstOrInit() (*model.OnlineTemplate, error) {
	if result, err := o.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.OnlineTemplate), nil
	}
}

func (o onlineTemplateDo) FirstOrCreate() (*model.OnlineTemplate, error) {
	if result, err := o.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.OnlineTemplate), nil
	}
}

func (o onlineTemplateDo) FindByPage(offset int, limit int) (result []*model.OnlineTemplate, count int64, err error) {
	result, err = o.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = o.Offset(-1).Limit(-1).Count()
	return
}

func (o onlineTemplateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = o.Count()
	if err != nil {
		return
	}

	err = o.Offset(offset).Limit(limit).Scan(result)
	return
}

func (o onlineTemplateDo) Scan(result interface{}) (err error) {
	return o.DO.Scan(result)
}

func (o onlineTemplateDo) Delete(models ...*model.OnlineTemplate) (result gen.ResultInfo, err error) {
	return o.DO.Delete(models)
}

func (o *onlineTemplateDo) withDO(do gen.Dao) *onlineTemplateDo {
	o.DO = *do.(*gen.DO)
	return o
}
