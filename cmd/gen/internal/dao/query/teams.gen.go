// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"fp-browser/internal/dao/model"
)

func newTeam(db *gorm.DB, opts ...gen.DOOption) team {
	_team := team{}

	_team.teamDo.UseDB(db, opts...)
	_team.teamDo.UseModel(&model.Team{})

	tableName := _team.teamDo.TableName()
	_team.ALL = field.NewAsterisk(tableName)
	_team.ID = field.NewInt32(tableName, "id")
	_team.Name = field.NewString(tableName, "name")
	_team.OwnerID = field.NewInt32(tableName, "owner_id")
	_team.CreatedAt = field.NewTime(tableName, "created_at")
	_team.UpdatedAt = field.NewTime(tableName, "updated_at")

	_team.fillFieldMap()

	return _team
}

type team struct {
	teamDo

	ALL       field.Asterisk
	ID        field.Int32  // 团队 ID，自增主键
	Name      field.String // 团队名称，最长 50 字符
	OwnerID   field.Int32  // 团队创建者的用户 ID
	CreatedAt field.Time   // 团队创建时间
	UpdatedAt field.Time   // 团队最后更新时间

	fieldMap map[string]field.Expr
}

func (t team) Table(newTableName string) *team {
	t.teamDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t team) As(alias string) *team {
	t.teamDo.DO = *(t.teamDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *team) updateTableName(table string) *team {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewInt32(table, "id")
	t.Name = field.NewString(table, "name")
	t.OwnerID = field.NewInt32(table, "owner_id")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")

	t.fillFieldMap()

	return t
}

func (t *team) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *team) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 5)
	t.fieldMap["id"] = t.ID
	t.fieldMap["name"] = t.Name
	t.fieldMap["owner_id"] = t.OwnerID
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
}

func (t team) clone(db *gorm.DB) team {
	t.teamDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t team) replaceDB(db *gorm.DB) team {
	t.teamDo.ReplaceDB(db)
	return t
}

type teamDo struct{ gen.DO }

type ITeamDo interface {
	gen.SubQuery
	Debug() ITeamDo
	WithContext(ctx context.Context) ITeamDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ITeamDo
	WriteDB() ITeamDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ITeamDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ITeamDo
	Not(conds ...gen.Condition) ITeamDo
	Or(conds ...gen.Condition) ITeamDo
	Select(conds ...field.Expr) ITeamDo
	Where(conds ...gen.Condition) ITeamDo
	Order(conds ...field.Expr) ITeamDo
	Distinct(cols ...field.Expr) ITeamDo
	Omit(cols ...field.Expr) ITeamDo
	Join(table schema.Tabler, on ...field.Expr) ITeamDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ITeamDo
	RightJoin(table schema.Tabler, on ...field.Expr) ITeamDo
	Group(cols ...field.Expr) ITeamDo
	Having(conds ...gen.Condition) ITeamDo
	Limit(limit int) ITeamDo
	Offset(offset int) ITeamDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ITeamDo
	Unscoped() ITeamDo
	Create(values ...*model.Team) error
	CreateInBatches(values []*model.Team, batchSize int) error
	Save(values ...*model.Team) error
	First() (*model.Team, error)
	Take() (*model.Team, error)
	Last() (*model.Team, error)
	Find() ([]*model.Team, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Team, err error)
	FindInBatches(result *[]*model.Team, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Team) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ITeamDo
	Assign(attrs ...field.AssignExpr) ITeamDo
	Joins(fields ...field.RelationField) ITeamDo
	Preload(fields ...field.RelationField) ITeamDo
	FirstOrInit() (*model.Team, error)
	FirstOrCreate() (*model.Team, error)
	FindByPage(offset int, limit int) (result []*model.Team, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ITeamDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t teamDo) Debug() ITeamDo {
	return t.withDO(t.DO.Debug())
}

func (t teamDo) WithContext(ctx context.Context) ITeamDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t teamDo) ReadDB() ITeamDo {
	return t.Clauses(dbresolver.Read)
}

func (t teamDo) WriteDB() ITeamDo {
	return t.Clauses(dbresolver.Write)
}

func (t teamDo) Session(config *gorm.Session) ITeamDo {
	return t.withDO(t.DO.Session(config))
}

func (t teamDo) Clauses(conds ...clause.Expression) ITeamDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t teamDo) Returning(value interface{}, columns ...string) ITeamDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t teamDo) Not(conds ...gen.Condition) ITeamDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t teamDo) Or(conds ...gen.Condition) ITeamDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t teamDo) Select(conds ...field.Expr) ITeamDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t teamDo) Where(conds ...gen.Condition) ITeamDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t teamDo) Order(conds ...field.Expr) ITeamDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t teamDo) Distinct(cols ...field.Expr) ITeamDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t teamDo) Omit(cols ...field.Expr) ITeamDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t teamDo) Join(table schema.Tabler, on ...field.Expr) ITeamDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t teamDo) LeftJoin(table schema.Tabler, on ...field.Expr) ITeamDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t teamDo) RightJoin(table schema.Tabler, on ...field.Expr) ITeamDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t teamDo) Group(cols ...field.Expr) ITeamDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t teamDo) Having(conds ...gen.Condition) ITeamDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t teamDo) Limit(limit int) ITeamDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t teamDo) Offset(offset int) ITeamDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t teamDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ITeamDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t teamDo) Unscoped() ITeamDo {
	return t.withDO(t.DO.Unscoped())
}

func (t teamDo) Create(values ...*model.Team) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t teamDo) CreateInBatches(values []*model.Team, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t teamDo) Save(values ...*model.Team) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t teamDo) First() (*model.Team, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Team), nil
	}
}

func (t teamDo) Take() (*model.Team, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Team), nil
	}
}

func (t teamDo) Last() (*model.Team, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Team), nil
	}
}

func (t teamDo) Find() ([]*model.Team, error) {
	result, err := t.DO.Find()
	return result.([]*model.Team), err
}

func (t teamDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Team, err error) {
	buf := make([]*model.Team, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t teamDo) FindInBatches(result *[]*model.Team, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t teamDo) Attrs(attrs ...field.AssignExpr) ITeamDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t teamDo) Assign(attrs ...field.AssignExpr) ITeamDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t teamDo) Joins(fields ...field.RelationField) ITeamDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t teamDo) Preload(fields ...field.RelationField) ITeamDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t teamDo) FirstOrInit() (*model.Team, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Team), nil
	}
}

func (t teamDo) FirstOrCreate() (*model.Team, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Team), nil
	}
}

func (t teamDo) FindByPage(offset int, limit int) (result []*model.Team, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t teamDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t teamDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t teamDo) Delete(models ...*model.Team) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *teamDo) withDO(do gen.Dao) *teamDo {
	t.DO = *do.(*gen.DO)
	return t
}
