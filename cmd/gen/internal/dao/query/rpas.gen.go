// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"fp-browser/cmd/gen/internal/dao/model"
)

func newRpa(db *gorm.DB, opts ...gen.DOOption) rpa {
	_rpa := rpa{}

	_rpa.rpaDo.UseDB(db, opts...)
	_rpa.rpaDo.UseModel(&model.Rpa{})

	tableName := _rpa.rpaDo.TableName()
	_rpa.ALL = field.NewAsterisk(tableName)
	_rpa.ID = field.NewInt32(tableName, "id")
	_rpa.Name = field.NewString(tableName, "name")
	_rpa.Description = field.NewString(tableName, "description")
	_rpa.ConfigFile = field.NewString(tableName, "config_file")
	_rpa.TeamID = field.NewInt32(tableName, "team_id")
	_rpa.CreatedBy = field.NewInt32(tableName, "created_by")
	_rpa.IsActive = field.NewBool(tableName, "is_active")
	_rpa.IsDeleted = field.NewBool(tableName, "is_deleted")
	_rpa.CreatedAt = field.NewTime(tableName, "created_at")
	_rpa.UpdatedAt = field.NewTime(tableName, "updated_at")

	_rpa.fillFieldMap()

	return _rpa
}

type rpa struct {
	rpaDo

	ALL         field.Asterisk
	ID          field.Int32  // RPA配置ID，主键
	Name        field.String // RPA配置名称
	Description field.String // RPA配置备注描述
	ConfigFile  field.String // YAML格式的配置文件内容
	TeamID      field.Int32  // 所属团队ID
	CreatedBy   field.Int32  // 创建者用户ID
	IsActive    field.Bool   // 是否启用
	IsDeleted   field.Bool   // 是否删除（软删除）
	CreatedAt   field.Time   // 记录创建时间
	UpdatedAt   field.Time   // 记录最近更新时间

	fieldMap map[string]field.Expr
}

func (r rpa) Table(newTableName string) *rpa {
	r.rpaDo.UseTable(newTableName)
	return r.updateTableName(newTableName)
}

func (r rpa) As(alias string) *rpa {
	r.rpaDo.DO = *(r.rpaDo.As(alias).(*gen.DO))
	return r.updateTableName(alias)
}

func (r *rpa) updateTableName(table string) *rpa {
	r.ALL = field.NewAsterisk(table)
	r.ID = field.NewInt32(table, "id")
	r.Name = field.NewString(table, "name")
	r.Description = field.NewString(table, "description")
	r.ConfigFile = field.NewString(table, "config_file")
	r.TeamID = field.NewInt32(table, "team_id")
	r.CreatedBy = field.NewInt32(table, "created_by")
	r.IsActive = field.NewBool(table, "is_active")
	r.IsDeleted = field.NewBool(table, "is_deleted")
	r.CreatedAt = field.NewTime(table, "created_at")
	r.UpdatedAt = field.NewTime(table, "updated_at")

	r.fillFieldMap()

	return r
}

func (r *rpa) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := r.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (r *rpa) fillFieldMap() {
	r.fieldMap = make(map[string]field.Expr, 10)
	r.fieldMap["id"] = r.ID
	r.fieldMap["name"] = r.Name
	r.fieldMap["description"] = r.Description
	r.fieldMap["config_file"] = r.ConfigFile
	r.fieldMap["team_id"] = r.TeamID
	r.fieldMap["created_by"] = r.CreatedBy
	r.fieldMap["is_active"] = r.IsActive
	r.fieldMap["is_deleted"] = r.IsDeleted
	r.fieldMap["created_at"] = r.CreatedAt
	r.fieldMap["updated_at"] = r.UpdatedAt
}

func (r rpa) clone(db *gorm.DB) rpa {
	r.rpaDo.ReplaceConnPool(db.Statement.ConnPool)
	return r
}

func (r rpa) replaceDB(db *gorm.DB) rpa {
	r.rpaDo.ReplaceDB(db)
	return r
}

type rpaDo struct{ gen.DO }

type IRpaDo interface {
	gen.SubQuery
	Debug() IRpaDo
	WithContext(ctx context.Context) IRpaDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IRpaDo
	WriteDB() IRpaDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IRpaDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IRpaDo
	Not(conds ...gen.Condition) IRpaDo
	Or(conds ...gen.Condition) IRpaDo
	Select(conds ...field.Expr) IRpaDo
	Where(conds ...gen.Condition) IRpaDo
	Order(conds ...field.Expr) IRpaDo
	Distinct(cols ...field.Expr) IRpaDo
	Omit(cols ...field.Expr) IRpaDo
	Join(table schema.Tabler, on ...field.Expr) IRpaDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IRpaDo
	RightJoin(table schema.Tabler, on ...field.Expr) IRpaDo
	Group(cols ...field.Expr) IRpaDo
	Having(conds ...gen.Condition) IRpaDo
	Limit(limit int) IRpaDo
	Offset(offset int) IRpaDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IRpaDo
	Unscoped() IRpaDo
	Create(values ...*model.Rpa) error
	CreateInBatches(values []*model.Rpa, batchSize int) error
	Save(values ...*model.Rpa) error
	First() (*model.Rpa, error)
	Take() (*model.Rpa, error)
	Last() (*model.Rpa, error)
	Find() ([]*model.Rpa, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Rpa, err error)
	FindInBatches(result *[]*model.Rpa, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Rpa) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IRpaDo
	Assign(attrs ...field.AssignExpr) IRpaDo
	Joins(fields ...field.RelationField) IRpaDo
	Preload(fields ...field.RelationField) IRpaDo
	FirstOrInit() (*model.Rpa, error)
	FirstOrCreate() (*model.Rpa, error)
	FindByPage(offset int, limit int) (result []*model.Rpa, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IRpaDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (r rpaDo) Debug() IRpaDo {
	return r.withDO(r.DO.Debug())
}

func (r rpaDo) WithContext(ctx context.Context) IRpaDo {
	return r.withDO(r.DO.WithContext(ctx))
}

func (r rpaDo) ReadDB() IRpaDo {
	return r.Clauses(dbresolver.Read)
}

func (r rpaDo) WriteDB() IRpaDo {
	return r.Clauses(dbresolver.Write)
}

func (r rpaDo) Session(config *gorm.Session) IRpaDo {
	return r.withDO(r.DO.Session(config))
}

func (r rpaDo) Clauses(conds ...clause.Expression) IRpaDo {
	return r.withDO(r.DO.Clauses(conds...))
}

func (r rpaDo) Returning(value interface{}, columns ...string) IRpaDo {
	return r.withDO(r.DO.Returning(value, columns...))
}

func (r rpaDo) Not(conds ...gen.Condition) IRpaDo {
	return r.withDO(r.DO.Not(conds...))
}

func (r rpaDo) Or(conds ...gen.Condition) IRpaDo {
	return r.withDO(r.DO.Or(conds...))
}

func (r rpaDo) Select(conds ...field.Expr) IRpaDo {
	return r.withDO(r.DO.Select(conds...))
}

func (r rpaDo) Where(conds ...gen.Condition) IRpaDo {
	return r.withDO(r.DO.Where(conds...))
}

func (r rpaDo) Order(conds ...field.Expr) IRpaDo {
	return r.withDO(r.DO.Order(conds...))
}

func (r rpaDo) Distinct(cols ...field.Expr) IRpaDo {
	return r.withDO(r.DO.Distinct(cols...))
}

func (r rpaDo) Omit(cols ...field.Expr) IRpaDo {
	return r.withDO(r.DO.Omit(cols...))
}

func (r rpaDo) Join(table schema.Tabler, on ...field.Expr) IRpaDo {
	return r.withDO(r.DO.Join(table, on...))
}

func (r rpaDo) LeftJoin(table schema.Tabler, on ...field.Expr) IRpaDo {
	return r.withDO(r.DO.LeftJoin(table, on...))
}

func (r rpaDo) RightJoin(table schema.Tabler, on ...field.Expr) IRpaDo {
	return r.withDO(r.DO.RightJoin(table, on...))
}

func (r rpaDo) Group(cols ...field.Expr) IRpaDo {
	return r.withDO(r.DO.Group(cols...))
}

func (r rpaDo) Having(conds ...gen.Condition) IRpaDo {
	return r.withDO(r.DO.Having(conds...))
}

func (r rpaDo) Limit(limit int) IRpaDo {
	return r.withDO(r.DO.Limit(limit))
}

func (r rpaDo) Offset(offset int) IRpaDo {
	return r.withDO(r.DO.Offset(offset))
}

func (r rpaDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IRpaDo {
	return r.withDO(r.DO.Scopes(funcs...))
}

func (r rpaDo) Unscoped() IRpaDo {
	return r.withDO(r.DO.Unscoped())
}

func (r rpaDo) Create(values ...*model.Rpa) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Create(values)
}

func (r rpaDo) CreateInBatches(values []*model.Rpa, batchSize int) error {
	return r.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (r rpaDo) Save(values ...*model.Rpa) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Save(values)
}

func (r rpaDo) First() (*model.Rpa, error) {
	if result, err := r.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Rpa), nil
	}
}

func (r rpaDo) Take() (*model.Rpa, error) {
	if result, err := r.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Rpa), nil
	}
}

func (r rpaDo) Last() (*model.Rpa, error) {
	if result, err := r.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Rpa), nil
	}
}

func (r rpaDo) Find() ([]*model.Rpa, error) {
	result, err := r.DO.Find()
	return result.([]*model.Rpa), err
}

func (r rpaDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Rpa, err error) {
	buf := make([]*model.Rpa, 0, batchSize)
	err = r.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (r rpaDo) FindInBatches(result *[]*model.Rpa, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return r.DO.FindInBatches(result, batchSize, fc)
}

func (r rpaDo) Attrs(attrs ...field.AssignExpr) IRpaDo {
	return r.withDO(r.DO.Attrs(attrs...))
}

func (r rpaDo) Assign(attrs ...field.AssignExpr) IRpaDo {
	return r.withDO(r.DO.Assign(attrs...))
}

func (r rpaDo) Joins(fields ...field.RelationField) IRpaDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Joins(_f))
	}
	return &r
}

func (r rpaDo) Preload(fields ...field.RelationField) IRpaDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Preload(_f))
	}
	return &r
}

func (r rpaDo) FirstOrInit() (*model.Rpa, error) {
	if result, err := r.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Rpa), nil
	}
}

func (r rpaDo) FirstOrCreate() (*model.Rpa, error) {
	if result, err := r.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Rpa), nil
	}
}

func (r rpaDo) FindByPage(offset int, limit int) (result []*model.Rpa, count int64, err error) {
	result, err = r.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = r.Offset(-1).Limit(-1).Count()
	return
}

func (r rpaDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = r.Count()
	if err != nil {
		return
	}

	err = r.Offset(offset).Limit(limit).Scan(result)
	return
}

func (r rpaDo) Scan(result interface{}) (err error) {
	return r.DO.Scan(result)
}

func (r rpaDo) Delete(models ...*model.Rpa) (result gen.ResultInfo, err error) {
	return r.DO.Delete(models)
}

func (r *rpaDo) withDO(do gen.Dao) *rpaDo {
	r.DO = *do.(*gen.DO)
	return r
}
