// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"fp-browser/cmd/gen/internal/dao/model"
)

func newUserBalance(db *gorm.DB, opts ...gen.DOOption) userBalance {
	_userBalance := userBalance{}

	_userBalance.userBalanceDo.UseDB(db, opts...)
	_userBalance.userBalanceDo.UseModel(&model.UserBalance{})

	tableName := _userBalance.userBalanceDo.TableName()
	_userBalance.ALL = field.NewAsterisk(tableName)
	_userBalance.UserID = field.NewInt32(tableName, "user_id")
	_userBalance.WalletAmount = field.NewInt64(tableName, "wallet_amount")
	_userBalance.UpdatedAt = field.NewTime(tableName, "updated_at")

	_userBalance.fillFieldMap()

	return _userBalance
}

type userBalance struct {
	userBalanceDo

	ALL          field.Asterisk
	UserID       field.Int32 // 用户 ID，主键
	WalletAmount field.Int64 // 钱包余额（单位：分），可以为负
	UpdatedAt    field.Time  // 最后更新时间

	fieldMap map[string]field.Expr
}

func (u userBalance) Table(newTableName string) *userBalance {
	u.userBalanceDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userBalance) As(alias string) *userBalance {
	u.userBalanceDo.DO = *(u.userBalanceDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userBalance) updateTableName(table string) *userBalance {
	u.ALL = field.NewAsterisk(table)
	u.UserID = field.NewInt32(table, "user_id")
	u.WalletAmount = field.NewInt64(table, "wallet_amount")
	u.UpdatedAt = field.NewTime(table, "updated_at")

	u.fillFieldMap()

	return u
}

func (u *userBalance) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userBalance) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 3)
	u.fieldMap["user_id"] = u.UserID
	u.fieldMap["wallet_amount"] = u.WalletAmount
	u.fieldMap["updated_at"] = u.UpdatedAt
}

func (u userBalance) clone(db *gorm.DB) userBalance {
	u.userBalanceDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userBalance) replaceDB(db *gorm.DB) userBalance {
	u.userBalanceDo.ReplaceDB(db)
	return u
}

type userBalanceDo struct{ gen.DO }

type IUserBalanceDo interface {
	gen.SubQuery
	Debug() IUserBalanceDo
	WithContext(ctx context.Context) IUserBalanceDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IUserBalanceDo
	WriteDB() IUserBalanceDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IUserBalanceDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IUserBalanceDo
	Not(conds ...gen.Condition) IUserBalanceDo
	Or(conds ...gen.Condition) IUserBalanceDo
	Select(conds ...field.Expr) IUserBalanceDo
	Where(conds ...gen.Condition) IUserBalanceDo
	Order(conds ...field.Expr) IUserBalanceDo
	Distinct(cols ...field.Expr) IUserBalanceDo
	Omit(cols ...field.Expr) IUserBalanceDo
	Join(table schema.Tabler, on ...field.Expr) IUserBalanceDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IUserBalanceDo
	RightJoin(table schema.Tabler, on ...field.Expr) IUserBalanceDo
	Group(cols ...field.Expr) IUserBalanceDo
	Having(conds ...gen.Condition) IUserBalanceDo
	Limit(limit int) IUserBalanceDo
	Offset(offset int) IUserBalanceDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IUserBalanceDo
	Unscoped() IUserBalanceDo
	Create(values ...*model.UserBalance) error
	CreateInBatches(values []*model.UserBalance, batchSize int) error
	Save(values ...*model.UserBalance) error
	First() (*model.UserBalance, error)
	Take() (*model.UserBalance, error)
	Last() (*model.UserBalance, error)
	Find() ([]*model.UserBalance, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserBalance, err error)
	FindInBatches(result *[]*model.UserBalance, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.UserBalance) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IUserBalanceDo
	Assign(attrs ...field.AssignExpr) IUserBalanceDo
	Joins(fields ...field.RelationField) IUserBalanceDo
	Preload(fields ...field.RelationField) IUserBalanceDo
	FirstOrInit() (*model.UserBalance, error)
	FirstOrCreate() (*model.UserBalance, error)
	FindByPage(offset int, limit int) (result []*model.UserBalance, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IUserBalanceDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (u userBalanceDo) Debug() IUserBalanceDo {
	return u.withDO(u.DO.Debug())
}

func (u userBalanceDo) WithContext(ctx context.Context) IUserBalanceDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userBalanceDo) ReadDB() IUserBalanceDo {
	return u.Clauses(dbresolver.Read)
}

func (u userBalanceDo) WriteDB() IUserBalanceDo {
	return u.Clauses(dbresolver.Write)
}

func (u userBalanceDo) Session(config *gorm.Session) IUserBalanceDo {
	return u.withDO(u.DO.Session(config))
}

func (u userBalanceDo) Clauses(conds ...clause.Expression) IUserBalanceDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userBalanceDo) Returning(value interface{}, columns ...string) IUserBalanceDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userBalanceDo) Not(conds ...gen.Condition) IUserBalanceDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userBalanceDo) Or(conds ...gen.Condition) IUserBalanceDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userBalanceDo) Select(conds ...field.Expr) IUserBalanceDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userBalanceDo) Where(conds ...gen.Condition) IUserBalanceDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userBalanceDo) Order(conds ...field.Expr) IUserBalanceDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userBalanceDo) Distinct(cols ...field.Expr) IUserBalanceDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userBalanceDo) Omit(cols ...field.Expr) IUserBalanceDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userBalanceDo) Join(table schema.Tabler, on ...field.Expr) IUserBalanceDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userBalanceDo) LeftJoin(table schema.Tabler, on ...field.Expr) IUserBalanceDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userBalanceDo) RightJoin(table schema.Tabler, on ...field.Expr) IUserBalanceDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userBalanceDo) Group(cols ...field.Expr) IUserBalanceDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userBalanceDo) Having(conds ...gen.Condition) IUserBalanceDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userBalanceDo) Limit(limit int) IUserBalanceDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userBalanceDo) Offset(offset int) IUserBalanceDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userBalanceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IUserBalanceDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userBalanceDo) Unscoped() IUserBalanceDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userBalanceDo) Create(values ...*model.UserBalance) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userBalanceDo) CreateInBatches(values []*model.UserBalance, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userBalanceDo) Save(values ...*model.UserBalance) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userBalanceDo) First() (*model.UserBalance, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserBalance), nil
	}
}

func (u userBalanceDo) Take() (*model.UserBalance, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserBalance), nil
	}
}

func (u userBalanceDo) Last() (*model.UserBalance, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserBalance), nil
	}
}

func (u userBalanceDo) Find() ([]*model.UserBalance, error) {
	result, err := u.DO.Find()
	return result.([]*model.UserBalance), err
}

func (u userBalanceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserBalance, err error) {
	buf := make([]*model.UserBalance, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userBalanceDo) FindInBatches(result *[]*model.UserBalance, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userBalanceDo) Attrs(attrs ...field.AssignExpr) IUserBalanceDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userBalanceDo) Assign(attrs ...field.AssignExpr) IUserBalanceDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userBalanceDo) Joins(fields ...field.RelationField) IUserBalanceDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userBalanceDo) Preload(fields ...field.RelationField) IUserBalanceDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userBalanceDo) FirstOrInit() (*model.UserBalance, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserBalance), nil
	}
}

func (u userBalanceDo) FirstOrCreate() (*model.UserBalance, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserBalance), nil
	}
}

func (u userBalanceDo) FindByPage(offset int, limit int) (result []*model.UserBalance, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userBalanceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userBalanceDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userBalanceDo) Delete(models ...*model.UserBalance) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userBalanceDo) withDO(do gen.Dao) *userBalanceDo {
	u.DO = *do.(*gen.DO)
	return u
}
