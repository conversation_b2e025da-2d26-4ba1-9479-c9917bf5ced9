// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"fp-browser/cmd/gen/internal/dao/model"
)

func newCoupon(db *gorm.DB, opts ...gen.DOOption) coupon {
	_coupon := coupon{}

	_coupon.couponDo.UseDB(db, opts...)
	_coupon.couponDo.UseModel(&model.Coupon{})

	tableName := _coupon.couponDo.TableName()
	_coupon.ALL = field.NewAsterisk(tableName)
	_coupon.ID = field.NewInt32(tableName, "id")
	_coupon.CouponCode = field.NewString(tableName, "coupon_code")
	_coupon.Name = field.NewString(tableName, "name")
	_coupon.Type = field.NewInt16(tableName, "type")
	_coupon.Value = field.NewInt32(tableName, "value")
	_coupon.MaxUsage = field.NewInt32(tableName, "max_usage")
	_coupon.MaxUsagePerUser = field.NewInt32(tableName, "max_usage_per_user")
	_coupon.TargetSubscriptionID = field.NewInt32(tableName, "target_subscription_id")
	_coupon.PromoterID = field.NewInt32(tableName, "promoter_id")
	_coupon.AutoBindPromoter = field.NewBool(tableName, "auto_bind_promoter")
	_coupon.CreatedAt = field.NewTime(tableName, "created_at")
	_coupon.ItemType = field.NewInt16(tableName, "item_type")

	_coupon.fillFieldMap()

	return _coupon
}

type coupon struct {
	couponDo

	ALL                  field.Asterisk
	ID                   field.Int32  // 自增主键ID
	CouponCode           field.String // 优惠券唯一ID（字符串），供用户使用输入
	Name                 field.String // 优惠券名称，展示给用户
	Type                 field.Int16  // 优惠券类型：0 表示固定金额（fixed），1 表示百分比折扣（percent）
	Value                field.Int32  // 优惠额度：单位为分（如100 = 1元）或百分比（如10 = 10%）
	MaxUsage             field.Int32  // 优惠券最大使用总次数
	MaxUsagePerUser      field.Int32  // 每个用户最多使用次数
	TargetSubscriptionID field.Int32  // 绑定的套餐ID，空则表示所有套餐均可使用
	PromoterID           field.Int32  // 优惠券关联的推广者ID，可为空
	AutoBindPromoter     field.Bool   // 是否在用户使用该券时自动绑定其推广关系
	CreatedAt            field.Time   // 创建时间
	ItemType             field.Int16  // 1为套餐.2为代理

	fieldMap map[string]field.Expr
}

func (c coupon) Table(newTableName string) *coupon {
	c.couponDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c coupon) As(alias string) *coupon {
	c.couponDo.DO = *(c.couponDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *coupon) updateTableName(table string) *coupon {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt32(table, "id")
	c.CouponCode = field.NewString(table, "coupon_code")
	c.Name = field.NewString(table, "name")
	c.Type = field.NewInt16(table, "type")
	c.Value = field.NewInt32(table, "value")
	c.MaxUsage = field.NewInt32(table, "max_usage")
	c.MaxUsagePerUser = field.NewInt32(table, "max_usage_per_user")
	c.TargetSubscriptionID = field.NewInt32(table, "target_subscription_id")
	c.PromoterID = field.NewInt32(table, "promoter_id")
	c.AutoBindPromoter = field.NewBool(table, "auto_bind_promoter")
	c.CreatedAt = field.NewTime(table, "created_at")
	c.ItemType = field.NewInt16(table, "item_type")

	c.fillFieldMap()

	return c
}

func (c *coupon) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *coupon) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 12)
	c.fieldMap["id"] = c.ID
	c.fieldMap["coupon_code"] = c.CouponCode
	c.fieldMap["name"] = c.Name
	c.fieldMap["type"] = c.Type
	c.fieldMap["value"] = c.Value
	c.fieldMap["max_usage"] = c.MaxUsage
	c.fieldMap["max_usage_per_user"] = c.MaxUsagePerUser
	c.fieldMap["target_subscription_id"] = c.TargetSubscriptionID
	c.fieldMap["promoter_id"] = c.PromoterID
	c.fieldMap["auto_bind_promoter"] = c.AutoBindPromoter
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["item_type"] = c.ItemType
}

func (c coupon) clone(db *gorm.DB) coupon {
	c.couponDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c coupon) replaceDB(db *gorm.DB) coupon {
	c.couponDo.ReplaceDB(db)
	return c
}

type couponDo struct{ gen.DO }

type ICouponDo interface {
	gen.SubQuery
	Debug() ICouponDo
	WithContext(ctx context.Context) ICouponDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ICouponDo
	WriteDB() ICouponDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ICouponDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ICouponDo
	Not(conds ...gen.Condition) ICouponDo
	Or(conds ...gen.Condition) ICouponDo
	Select(conds ...field.Expr) ICouponDo
	Where(conds ...gen.Condition) ICouponDo
	Order(conds ...field.Expr) ICouponDo
	Distinct(cols ...field.Expr) ICouponDo
	Omit(cols ...field.Expr) ICouponDo
	Join(table schema.Tabler, on ...field.Expr) ICouponDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ICouponDo
	RightJoin(table schema.Tabler, on ...field.Expr) ICouponDo
	Group(cols ...field.Expr) ICouponDo
	Having(conds ...gen.Condition) ICouponDo
	Limit(limit int) ICouponDo
	Offset(offset int) ICouponDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ICouponDo
	Unscoped() ICouponDo
	Create(values ...*model.Coupon) error
	CreateInBatches(values []*model.Coupon, batchSize int) error
	Save(values ...*model.Coupon) error
	First() (*model.Coupon, error)
	Take() (*model.Coupon, error)
	Last() (*model.Coupon, error)
	Find() ([]*model.Coupon, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Coupon, err error)
	FindInBatches(result *[]*model.Coupon, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Coupon) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ICouponDo
	Assign(attrs ...field.AssignExpr) ICouponDo
	Joins(fields ...field.RelationField) ICouponDo
	Preload(fields ...field.RelationField) ICouponDo
	FirstOrInit() (*model.Coupon, error)
	FirstOrCreate() (*model.Coupon, error)
	FindByPage(offset int, limit int) (result []*model.Coupon, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ICouponDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c couponDo) Debug() ICouponDo {
	return c.withDO(c.DO.Debug())
}

func (c couponDo) WithContext(ctx context.Context) ICouponDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c couponDo) ReadDB() ICouponDo {
	return c.Clauses(dbresolver.Read)
}

func (c couponDo) WriteDB() ICouponDo {
	return c.Clauses(dbresolver.Write)
}

func (c couponDo) Session(config *gorm.Session) ICouponDo {
	return c.withDO(c.DO.Session(config))
}

func (c couponDo) Clauses(conds ...clause.Expression) ICouponDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c couponDo) Returning(value interface{}, columns ...string) ICouponDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c couponDo) Not(conds ...gen.Condition) ICouponDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c couponDo) Or(conds ...gen.Condition) ICouponDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c couponDo) Select(conds ...field.Expr) ICouponDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c couponDo) Where(conds ...gen.Condition) ICouponDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c couponDo) Order(conds ...field.Expr) ICouponDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c couponDo) Distinct(cols ...field.Expr) ICouponDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c couponDo) Omit(cols ...field.Expr) ICouponDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c couponDo) Join(table schema.Tabler, on ...field.Expr) ICouponDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c couponDo) LeftJoin(table schema.Tabler, on ...field.Expr) ICouponDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c couponDo) RightJoin(table schema.Tabler, on ...field.Expr) ICouponDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c couponDo) Group(cols ...field.Expr) ICouponDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c couponDo) Having(conds ...gen.Condition) ICouponDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c couponDo) Limit(limit int) ICouponDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c couponDo) Offset(offset int) ICouponDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c couponDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ICouponDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c couponDo) Unscoped() ICouponDo {
	return c.withDO(c.DO.Unscoped())
}

func (c couponDo) Create(values ...*model.Coupon) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c couponDo) CreateInBatches(values []*model.Coupon, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c couponDo) Save(values ...*model.Coupon) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c couponDo) First() (*model.Coupon, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Coupon), nil
	}
}

func (c couponDo) Take() (*model.Coupon, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Coupon), nil
	}
}

func (c couponDo) Last() (*model.Coupon, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Coupon), nil
	}
}

func (c couponDo) Find() ([]*model.Coupon, error) {
	result, err := c.DO.Find()
	return result.([]*model.Coupon), err
}

func (c couponDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Coupon, err error) {
	buf := make([]*model.Coupon, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c couponDo) FindInBatches(result *[]*model.Coupon, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c couponDo) Attrs(attrs ...field.AssignExpr) ICouponDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c couponDo) Assign(attrs ...field.AssignExpr) ICouponDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c couponDo) Joins(fields ...field.RelationField) ICouponDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c couponDo) Preload(fields ...field.RelationField) ICouponDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c couponDo) FirstOrInit() (*model.Coupon, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Coupon), nil
	}
}

func (c couponDo) FirstOrCreate() (*model.Coupon, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Coupon), nil
	}
}

func (c couponDo) FindByPage(offset int, limit int) (result []*model.Coupon, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c couponDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c couponDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c couponDo) Delete(models ...*model.Coupon) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *couponDo) withDO(do gen.Dao) *couponDo {
	c.DO = *do.(*gen.DO)
	return c
}
