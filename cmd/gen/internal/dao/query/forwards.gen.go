// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"fp-browser/cmd/gen/internal/dao/model"
)

func newForward(db *gorm.DB, opts ...gen.DOOption) forward {
	_forward := forward{}

	_forward.forwardDo.UseDB(db, opts...)
	_forward.forwardDo.UseModel(&model.Forward{})

	tableName := _forward.forwardDo.TableName()
	_forward.ALL = field.NewAsterisk(tableName)
	_forward.ID = field.NewInt32(tableName, "id")
	_forward.Name = field.NewString(tableName, "name")
	_forward.Domain = field.NewString(tableName, "domain")
	_forward.Port = field.NewInt16(tableName, "port")
	_forward.CreatedAt = field.NewTime(tableName, "created_at")
	_forward.UpdatedAt = field.NewTime(tableName, "updated_at")

	_forward.fillFieldMap()

	return _forward
}

type forward struct {
	forwardDo

	ALL       field.Asterisk
	ID        field.Int32  // 自增 ID
	Name      field.String // 服务器名称，用于识别
	Domain    field.String // 目标域名或 IP
	Port      field.Int16  // 目标端口号
	CreatedAt field.Time   // 记录创建时间
	UpdatedAt field.Time   // 记录更新时间

	fieldMap map[string]field.Expr
}

func (f forward) Table(newTableName string) *forward {
	f.forwardDo.UseTable(newTableName)
	return f.updateTableName(newTableName)
}

func (f forward) As(alias string) *forward {
	f.forwardDo.DO = *(f.forwardDo.As(alias).(*gen.DO))
	return f.updateTableName(alias)
}

func (f *forward) updateTableName(table string) *forward {
	f.ALL = field.NewAsterisk(table)
	f.ID = field.NewInt32(table, "id")
	f.Name = field.NewString(table, "name")
	f.Domain = field.NewString(table, "domain")
	f.Port = field.NewInt16(table, "port")
	f.CreatedAt = field.NewTime(table, "created_at")
	f.UpdatedAt = field.NewTime(table, "updated_at")

	f.fillFieldMap()

	return f
}

func (f *forward) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := f.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (f *forward) fillFieldMap() {
	f.fieldMap = make(map[string]field.Expr, 6)
	f.fieldMap["id"] = f.ID
	f.fieldMap["name"] = f.Name
	f.fieldMap["domain"] = f.Domain
	f.fieldMap["port"] = f.Port
	f.fieldMap["created_at"] = f.CreatedAt
	f.fieldMap["updated_at"] = f.UpdatedAt
}

func (f forward) clone(db *gorm.DB) forward {
	f.forwardDo.ReplaceConnPool(db.Statement.ConnPool)
	return f
}

func (f forward) replaceDB(db *gorm.DB) forward {
	f.forwardDo.ReplaceDB(db)
	return f
}

type forwardDo struct{ gen.DO }

type IForwardDo interface {
	gen.SubQuery
	Debug() IForwardDo
	WithContext(ctx context.Context) IForwardDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IForwardDo
	WriteDB() IForwardDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IForwardDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IForwardDo
	Not(conds ...gen.Condition) IForwardDo
	Or(conds ...gen.Condition) IForwardDo
	Select(conds ...field.Expr) IForwardDo
	Where(conds ...gen.Condition) IForwardDo
	Order(conds ...field.Expr) IForwardDo
	Distinct(cols ...field.Expr) IForwardDo
	Omit(cols ...field.Expr) IForwardDo
	Join(table schema.Tabler, on ...field.Expr) IForwardDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IForwardDo
	RightJoin(table schema.Tabler, on ...field.Expr) IForwardDo
	Group(cols ...field.Expr) IForwardDo
	Having(conds ...gen.Condition) IForwardDo
	Limit(limit int) IForwardDo
	Offset(offset int) IForwardDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IForwardDo
	Unscoped() IForwardDo
	Create(values ...*model.Forward) error
	CreateInBatches(values []*model.Forward, batchSize int) error
	Save(values ...*model.Forward) error
	First() (*model.Forward, error)
	Take() (*model.Forward, error)
	Last() (*model.Forward, error)
	Find() ([]*model.Forward, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Forward, err error)
	FindInBatches(result *[]*model.Forward, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Forward) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IForwardDo
	Assign(attrs ...field.AssignExpr) IForwardDo
	Joins(fields ...field.RelationField) IForwardDo
	Preload(fields ...field.RelationField) IForwardDo
	FirstOrInit() (*model.Forward, error)
	FirstOrCreate() (*model.Forward, error)
	FindByPage(offset int, limit int) (result []*model.Forward, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IForwardDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (f forwardDo) Debug() IForwardDo {
	return f.withDO(f.DO.Debug())
}

func (f forwardDo) WithContext(ctx context.Context) IForwardDo {
	return f.withDO(f.DO.WithContext(ctx))
}

func (f forwardDo) ReadDB() IForwardDo {
	return f.Clauses(dbresolver.Read)
}

func (f forwardDo) WriteDB() IForwardDo {
	return f.Clauses(dbresolver.Write)
}

func (f forwardDo) Session(config *gorm.Session) IForwardDo {
	return f.withDO(f.DO.Session(config))
}

func (f forwardDo) Clauses(conds ...clause.Expression) IForwardDo {
	return f.withDO(f.DO.Clauses(conds...))
}

func (f forwardDo) Returning(value interface{}, columns ...string) IForwardDo {
	return f.withDO(f.DO.Returning(value, columns...))
}

func (f forwardDo) Not(conds ...gen.Condition) IForwardDo {
	return f.withDO(f.DO.Not(conds...))
}

func (f forwardDo) Or(conds ...gen.Condition) IForwardDo {
	return f.withDO(f.DO.Or(conds...))
}

func (f forwardDo) Select(conds ...field.Expr) IForwardDo {
	return f.withDO(f.DO.Select(conds...))
}

func (f forwardDo) Where(conds ...gen.Condition) IForwardDo {
	return f.withDO(f.DO.Where(conds...))
}

func (f forwardDo) Order(conds ...field.Expr) IForwardDo {
	return f.withDO(f.DO.Order(conds...))
}

func (f forwardDo) Distinct(cols ...field.Expr) IForwardDo {
	return f.withDO(f.DO.Distinct(cols...))
}

func (f forwardDo) Omit(cols ...field.Expr) IForwardDo {
	return f.withDO(f.DO.Omit(cols...))
}

func (f forwardDo) Join(table schema.Tabler, on ...field.Expr) IForwardDo {
	return f.withDO(f.DO.Join(table, on...))
}

func (f forwardDo) LeftJoin(table schema.Tabler, on ...field.Expr) IForwardDo {
	return f.withDO(f.DO.LeftJoin(table, on...))
}

func (f forwardDo) RightJoin(table schema.Tabler, on ...field.Expr) IForwardDo {
	return f.withDO(f.DO.RightJoin(table, on...))
}

func (f forwardDo) Group(cols ...field.Expr) IForwardDo {
	return f.withDO(f.DO.Group(cols...))
}

func (f forwardDo) Having(conds ...gen.Condition) IForwardDo {
	return f.withDO(f.DO.Having(conds...))
}

func (f forwardDo) Limit(limit int) IForwardDo {
	return f.withDO(f.DO.Limit(limit))
}

func (f forwardDo) Offset(offset int) IForwardDo {
	return f.withDO(f.DO.Offset(offset))
}

func (f forwardDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IForwardDo {
	return f.withDO(f.DO.Scopes(funcs...))
}

func (f forwardDo) Unscoped() IForwardDo {
	return f.withDO(f.DO.Unscoped())
}

func (f forwardDo) Create(values ...*model.Forward) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Create(values)
}

func (f forwardDo) CreateInBatches(values []*model.Forward, batchSize int) error {
	return f.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (f forwardDo) Save(values ...*model.Forward) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Save(values)
}

func (f forwardDo) First() (*model.Forward, error) {
	if result, err := f.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Forward), nil
	}
}

func (f forwardDo) Take() (*model.Forward, error) {
	if result, err := f.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Forward), nil
	}
}

func (f forwardDo) Last() (*model.Forward, error) {
	if result, err := f.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Forward), nil
	}
}

func (f forwardDo) Find() ([]*model.Forward, error) {
	result, err := f.DO.Find()
	return result.([]*model.Forward), err
}

func (f forwardDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Forward, err error) {
	buf := make([]*model.Forward, 0, batchSize)
	err = f.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (f forwardDo) FindInBatches(result *[]*model.Forward, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return f.DO.FindInBatches(result, batchSize, fc)
}

func (f forwardDo) Attrs(attrs ...field.AssignExpr) IForwardDo {
	return f.withDO(f.DO.Attrs(attrs...))
}

func (f forwardDo) Assign(attrs ...field.AssignExpr) IForwardDo {
	return f.withDO(f.DO.Assign(attrs...))
}

func (f forwardDo) Joins(fields ...field.RelationField) IForwardDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Joins(_f))
	}
	return &f
}

func (f forwardDo) Preload(fields ...field.RelationField) IForwardDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Preload(_f))
	}
	return &f
}

func (f forwardDo) FirstOrInit() (*model.Forward, error) {
	if result, err := f.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Forward), nil
	}
}

func (f forwardDo) FirstOrCreate() (*model.Forward, error) {
	if result, err := f.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Forward), nil
	}
}

func (f forwardDo) FindByPage(offset int, limit int) (result []*model.Forward, count int64, err error) {
	result, err = f.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = f.Offset(-1).Limit(-1).Count()
	return
}

func (f forwardDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = f.Count()
	if err != nil {
		return
	}

	err = f.Offset(offset).Limit(limit).Scan(result)
	return
}

func (f forwardDo) Scan(result interface{}) (err error) {
	return f.DO.Scan(result)
}

func (f forwardDo) Delete(models ...*model.Forward) (result gen.ResultInfo, err error) {
	return f.DO.Delete(models)
}

func (f *forwardDo) withDO(do gen.Dao) *forwardDo {
	f.DO = *do.(*gen.DO)
	return f
}
