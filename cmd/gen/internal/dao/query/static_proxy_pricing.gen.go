// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"fp-browser/cmd/gen/internal/dao/model"
)

func newStaticProxyPricing(db *gorm.DB, opts ...gen.DOOption) staticProxyPricing {
	_staticProxyPricing := staticProxyPricing{}

	_staticProxyPricing.staticProxyPricingDo.UseDB(db, opts...)
	_staticProxyPricing.staticProxyPricingDo.UseModel(&model.StaticProxyPricing{})

	tableName := _staticProxyPricing.staticProxyPricingDo.TableName()
	_staticProxyPricing.ALL = field.NewAsterisk(tableName)
	_staticProxyPricing.ID = field.NewInt32(tableName, "id")
	_staticProxyPricing.CountryCode = field.NewString(tableName, "country_code")
	_staticProxyPricing.CountryLocale = field.NewString(tableName, "country_locale")
	_staticProxyPricing.City = field.NewString(tableName, "city")
	_staticProxyPricing.CityLocale = field.NewString(tableName, "city_locale")
	_staticProxyPricing.Carrier = field.NewString(tableName, "carrier")
	_staticProxyPricing.CarrierLocale = field.NewString(tableName, "carrier_locale")
	_staticProxyPricing.Price = field.NewFloat64(tableName, "price")
	_staticProxyPricing.Currency = field.NewString(tableName, "currency")
	_staticProxyPricing.BillingPeriod = field.NewString(tableName, "billing_period")
	_staticProxyPricing.IPCount = field.NewInt32(tableName, "ip_count")
	_staticProxyPricing.IsActive = field.NewBool(tableName, "is_active")
	_staticProxyPricing.CreatedAt = field.NewTime(tableName, "created_at")
	_staticProxyPricing.UpdatedAt = field.NewTime(tableName, "updated_at")

	_staticProxyPricing.fillFieldMap()

	return _staticProxyPricing
}

type staticProxyPricing struct {
	staticProxyPricingDo

	ALL           field.Asterisk
	ID            field.Int32
	CountryCode   field.String
	CountryLocale field.String
	City          field.String
	CityLocale    field.String
	Carrier       field.String
	CarrierLocale field.String
	Price         field.Float64
	Currency      field.String
	BillingPeriod field.String
	IPCount       field.Int32
	IsActive      field.Bool
	CreatedAt     field.Time
	UpdatedAt     field.Time

	fieldMap map[string]field.Expr
}

func (s staticProxyPricing) Table(newTableName string) *staticProxyPricing {
	s.staticProxyPricingDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s staticProxyPricing) As(alias string) *staticProxyPricing {
	s.staticProxyPricingDo.DO = *(s.staticProxyPricingDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *staticProxyPricing) updateTableName(table string) *staticProxyPricing {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt32(table, "id")
	s.CountryCode = field.NewString(table, "country_code")
	s.CountryLocale = field.NewString(table, "country_locale")
	s.City = field.NewString(table, "city")
	s.CityLocale = field.NewString(table, "city_locale")
	s.Carrier = field.NewString(table, "carrier")
	s.CarrierLocale = field.NewString(table, "carrier_locale")
	s.Price = field.NewFloat64(table, "price")
	s.Currency = field.NewString(table, "currency")
	s.BillingPeriod = field.NewString(table, "billing_period")
	s.IPCount = field.NewInt32(table, "ip_count")
	s.IsActive = field.NewBool(table, "is_active")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedAt = field.NewTime(table, "updated_at")

	s.fillFieldMap()

	return s
}

func (s *staticProxyPricing) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *staticProxyPricing) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 14)
	s.fieldMap["id"] = s.ID
	s.fieldMap["country_code"] = s.CountryCode
	s.fieldMap["country_locale"] = s.CountryLocale
	s.fieldMap["city"] = s.City
	s.fieldMap["city_locale"] = s.CityLocale
	s.fieldMap["carrier"] = s.Carrier
	s.fieldMap["carrier_locale"] = s.CarrierLocale
	s.fieldMap["price"] = s.Price
	s.fieldMap["currency"] = s.Currency
	s.fieldMap["billing_period"] = s.BillingPeriod
	s.fieldMap["ip_count"] = s.IPCount
	s.fieldMap["is_active"] = s.IsActive
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
}

func (s staticProxyPricing) clone(db *gorm.DB) staticProxyPricing {
	s.staticProxyPricingDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s staticProxyPricing) replaceDB(db *gorm.DB) staticProxyPricing {
	s.staticProxyPricingDo.ReplaceDB(db)
	return s
}

type staticProxyPricingDo struct{ gen.DO }

type IStaticProxyPricingDo interface {
	gen.SubQuery
	Debug() IStaticProxyPricingDo
	WithContext(ctx context.Context) IStaticProxyPricingDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStaticProxyPricingDo
	WriteDB() IStaticProxyPricingDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStaticProxyPricingDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStaticProxyPricingDo
	Not(conds ...gen.Condition) IStaticProxyPricingDo
	Or(conds ...gen.Condition) IStaticProxyPricingDo
	Select(conds ...field.Expr) IStaticProxyPricingDo
	Where(conds ...gen.Condition) IStaticProxyPricingDo
	Order(conds ...field.Expr) IStaticProxyPricingDo
	Distinct(cols ...field.Expr) IStaticProxyPricingDo
	Omit(cols ...field.Expr) IStaticProxyPricingDo
	Join(table schema.Tabler, on ...field.Expr) IStaticProxyPricingDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStaticProxyPricingDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStaticProxyPricingDo
	Group(cols ...field.Expr) IStaticProxyPricingDo
	Having(conds ...gen.Condition) IStaticProxyPricingDo
	Limit(limit int) IStaticProxyPricingDo
	Offset(offset int) IStaticProxyPricingDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStaticProxyPricingDo
	Unscoped() IStaticProxyPricingDo
	Create(values ...*model.StaticProxyPricing) error
	CreateInBatches(values []*model.StaticProxyPricing, batchSize int) error
	Save(values ...*model.StaticProxyPricing) error
	First() (*model.StaticProxyPricing, error)
	Take() (*model.StaticProxyPricing, error)
	Last() (*model.StaticProxyPricing, error)
	Find() ([]*model.StaticProxyPricing, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StaticProxyPricing, err error)
	FindInBatches(result *[]*model.StaticProxyPricing, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.StaticProxyPricing) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStaticProxyPricingDo
	Assign(attrs ...field.AssignExpr) IStaticProxyPricingDo
	Joins(fields ...field.RelationField) IStaticProxyPricingDo
	Preload(fields ...field.RelationField) IStaticProxyPricingDo
	FirstOrInit() (*model.StaticProxyPricing, error)
	FirstOrCreate() (*model.StaticProxyPricing, error)
	FindByPage(offset int, limit int) (result []*model.StaticProxyPricing, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStaticProxyPricingDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s staticProxyPricingDo) Debug() IStaticProxyPricingDo {
	return s.withDO(s.DO.Debug())
}

func (s staticProxyPricingDo) WithContext(ctx context.Context) IStaticProxyPricingDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s staticProxyPricingDo) ReadDB() IStaticProxyPricingDo {
	return s.Clauses(dbresolver.Read)
}

func (s staticProxyPricingDo) WriteDB() IStaticProxyPricingDo {
	return s.Clauses(dbresolver.Write)
}

func (s staticProxyPricingDo) Session(config *gorm.Session) IStaticProxyPricingDo {
	return s.withDO(s.DO.Session(config))
}

func (s staticProxyPricingDo) Clauses(conds ...clause.Expression) IStaticProxyPricingDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s staticProxyPricingDo) Returning(value interface{}, columns ...string) IStaticProxyPricingDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s staticProxyPricingDo) Not(conds ...gen.Condition) IStaticProxyPricingDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s staticProxyPricingDo) Or(conds ...gen.Condition) IStaticProxyPricingDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s staticProxyPricingDo) Select(conds ...field.Expr) IStaticProxyPricingDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s staticProxyPricingDo) Where(conds ...gen.Condition) IStaticProxyPricingDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s staticProxyPricingDo) Order(conds ...field.Expr) IStaticProxyPricingDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s staticProxyPricingDo) Distinct(cols ...field.Expr) IStaticProxyPricingDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s staticProxyPricingDo) Omit(cols ...field.Expr) IStaticProxyPricingDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s staticProxyPricingDo) Join(table schema.Tabler, on ...field.Expr) IStaticProxyPricingDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s staticProxyPricingDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStaticProxyPricingDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s staticProxyPricingDo) RightJoin(table schema.Tabler, on ...field.Expr) IStaticProxyPricingDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s staticProxyPricingDo) Group(cols ...field.Expr) IStaticProxyPricingDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s staticProxyPricingDo) Having(conds ...gen.Condition) IStaticProxyPricingDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s staticProxyPricingDo) Limit(limit int) IStaticProxyPricingDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s staticProxyPricingDo) Offset(offset int) IStaticProxyPricingDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s staticProxyPricingDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStaticProxyPricingDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s staticProxyPricingDo) Unscoped() IStaticProxyPricingDo {
	return s.withDO(s.DO.Unscoped())
}

func (s staticProxyPricingDo) Create(values ...*model.StaticProxyPricing) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s staticProxyPricingDo) CreateInBatches(values []*model.StaticProxyPricing, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s staticProxyPricingDo) Save(values ...*model.StaticProxyPricing) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s staticProxyPricingDo) First() (*model.StaticProxyPricing, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StaticProxyPricing), nil
	}
}

func (s staticProxyPricingDo) Take() (*model.StaticProxyPricing, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StaticProxyPricing), nil
	}
}

func (s staticProxyPricingDo) Last() (*model.StaticProxyPricing, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StaticProxyPricing), nil
	}
}

func (s staticProxyPricingDo) Find() ([]*model.StaticProxyPricing, error) {
	result, err := s.DO.Find()
	return result.([]*model.StaticProxyPricing), err
}

func (s staticProxyPricingDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StaticProxyPricing, err error) {
	buf := make([]*model.StaticProxyPricing, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s staticProxyPricingDo) FindInBatches(result *[]*model.StaticProxyPricing, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s staticProxyPricingDo) Attrs(attrs ...field.AssignExpr) IStaticProxyPricingDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s staticProxyPricingDo) Assign(attrs ...field.AssignExpr) IStaticProxyPricingDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s staticProxyPricingDo) Joins(fields ...field.RelationField) IStaticProxyPricingDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s staticProxyPricingDo) Preload(fields ...field.RelationField) IStaticProxyPricingDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s staticProxyPricingDo) FirstOrInit() (*model.StaticProxyPricing, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StaticProxyPricing), nil
	}
}

func (s staticProxyPricingDo) FirstOrCreate() (*model.StaticProxyPricing, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StaticProxyPricing), nil
	}
}

func (s staticProxyPricingDo) FindByPage(offset int, limit int) (result []*model.StaticProxyPricing, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s staticProxyPricingDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s staticProxyPricingDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s staticProxyPricingDo) Delete(models ...*model.StaticProxyPricing) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *staticProxyPricingDo) withDO(do gen.Dao) *staticProxyPricingDo {
	s.DO = *do.(*gen.DO)
	return s
}
