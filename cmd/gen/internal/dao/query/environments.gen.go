// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"fp-browser/internal/dao/model"
)

func newEnvironment(db *gorm.DB, opts ...gen.DOOption) environment {
	_environment := environment{}

	_environment.environmentDo.UseDB(db, opts...)
	_environment.environmentDo.UseModel(&model.Environment{})

	tableName := _environment.environmentDo.TableName()
	_environment.ALL = field.NewAsterisk(tableName)
	_environment.ID = field.NewInt32(tableName, "id")
	_environment.TeamID = field.NewInt32(tableName, "team_id")
	_environment.Name = field.NewString(tableName, "name")
	_environment.UserID = field.NewInt32(tableName, "user_id")
	_environment.GroupID = field.NewInt32(tableName, "group_id")
	_environment.ProxyID = field.NewInt32(tableName, "proxy_id")
	_environment.ProxyType = field.NewInt16(tableName, "proxy_type")
	_environment.Platform = field.NewString(tableName, "platform")
	_environment.Parameters = field.NewString(tableName, "parameters")
	_environment.Storage = field.NewString(tableName, "storage")
	_environment.CreatedAt = field.NewTime(tableName, "created_at")
	_environment.UpdatedAt = field.NewTime(tableName, "updated_at")
	_environment.Tag = field.NewString(tableName, "tag")
	_environment.Comment = field.NewString(tableName, "comment")
	_environment.Sort = field.NewInt16(tableName, "sort")
	_environment.Size = field.NewInt32(tableName, "size")
	_environment.DeletedAt = field.NewField(tableName, "deleted_at")

	_environment.fillFieldMap()

	return _environment
}

type environment struct {
	environmentDo

	ALL        field.Asterisk
	ID         field.Int32  // 环境ID，自增主键
	TeamID     field.Int32  // 所属团队ID
	Name       field.String // 环境名称，100字符以内
	UserID     field.Int32  // 当前分配给的用户ID
	GroupID    field.Int32  // 所属分组ID，关联 Group 表
	ProxyID    field.Int32  // 代理ID，关联 Proxy 表
	ProxyType  field.Int16  // 代理类型，1平台代理，2自有代理
	Platform   field.String // 默认平台，例如 https://amazon.com/
	Parameters field.String // 环境参数，使用 JSONB 存储
	Storage    field.String // 备份路径，例如 AWS S3 地址
	CreatedAt  field.Time   // 创建时间
	UpdatedAt  field.Time   // 更新时间
	Tag        field.String // 存储环境标签，使用 JSONB 存储
	Comment    field.String // 备注
	Sort       field.Int16  // 环境排序
	Size       field.Int32  // 占用空间大小
	DeletedAt  field.Field  // 删除时间

	fieldMap map[string]field.Expr
}

func (e environment) Table(newTableName string) *environment {
	e.environmentDo.UseTable(newTableName)
	return e.updateTableName(newTableName)
}

func (e environment) As(alias string) *environment {
	e.environmentDo.DO = *(e.environmentDo.As(alias).(*gen.DO))
	return e.updateTableName(alias)
}

func (e *environment) updateTableName(table string) *environment {
	e.ALL = field.NewAsterisk(table)
	e.ID = field.NewInt32(table, "id")
	e.TeamID = field.NewInt32(table, "team_id")
	e.Name = field.NewString(table, "name")
	e.UserID = field.NewInt32(table, "user_id")
	e.GroupID = field.NewInt32(table, "group_id")
	e.ProxyID = field.NewInt32(table, "proxy_id")
	e.ProxyType = field.NewInt16(table, "proxy_type")
	e.Platform = field.NewString(table, "platform")
	e.Parameters = field.NewString(table, "parameters")
	e.Storage = field.NewString(table, "storage")
	e.CreatedAt = field.NewTime(table, "created_at")
	e.UpdatedAt = field.NewTime(table, "updated_at")
	e.Tag = field.NewString(table, "tag")
	e.Comment = field.NewString(table, "comment")
	e.Sort = field.NewInt16(table, "sort")
	e.Size = field.NewInt32(table, "size")
	e.DeletedAt = field.NewField(table, "deleted_at")

	e.fillFieldMap()

	return e
}

func (e *environment) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := e.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (e *environment) fillFieldMap() {
	e.fieldMap = make(map[string]field.Expr, 17)
	e.fieldMap["id"] = e.ID
	e.fieldMap["team_id"] = e.TeamID
	e.fieldMap["name"] = e.Name
	e.fieldMap["user_id"] = e.UserID
	e.fieldMap["group_id"] = e.GroupID
	e.fieldMap["proxy_id"] = e.ProxyID
	e.fieldMap["proxy_type"] = e.ProxyType
	e.fieldMap["platform"] = e.Platform
	e.fieldMap["parameters"] = e.Parameters
	e.fieldMap["storage"] = e.Storage
	e.fieldMap["created_at"] = e.CreatedAt
	e.fieldMap["updated_at"] = e.UpdatedAt
	e.fieldMap["tag"] = e.Tag
	e.fieldMap["comment"] = e.Comment
	e.fieldMap["sort"] = e.Sort
	e.fieldMap["size"] = e.Size
	e.fieldMap["deleted_at"] = e.DeletedAt
}

func (e environment) clone(db *gorm.DB) environment {
	e.environmentDo.ReplaceConnPool(db.Statement.ConnPool)
	return e
}

func (e environment) replaceDB(db *gorm.DB) environment {
	e.environmentDo.ReplaceDB(db)
	return e
}

type environmentDo struct{ gen.DO }

type IEnvironmentDo interface {
	gen.SubQuery
	Debug() IEnvironmentDo
	WithContext(ctx context.Context) IEnvironmentDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IEnvironmentDo
	WriteDB() IEnvironmentDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IEnvironmentDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IEnvironmentDo
	Not(conds ...gen.Condition) IEnvironmentDo
	Or(conds ...gen.Condition) IEnvironmentDo
	Select(conds ...field.Expr) IEnvironmentDo
	Where(conds ...gen.Condition) IEnvironmentDo
	Order(conds ...field.Expr) IEnvironmentDo
	Distinct(cols ...field.Expr) IEnvironmentDo
	Omit(cols ...field.Expr) IEnvironmentDo
	Join(table schema.Tabler, on ...field.Expr) IEnvironmentDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IEnvironmentDo
	RightJoin(table schema.Tabler, on ...field.Expr) IEnvironmentDo
	Group(cols ...field.Expr) IEnvironmentDo
	Having(conds ...gen.Condition) IEnvironmentDo
	Limit(limit int) IEnvironmentDo
	Offset(offset int) IEnvironmentDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IEnvironmentDo
	Unscoped() IEnvironmentDo
	Create(values ...*model.Environment) error
	CreateInBatches(values []*model.Environment, batchSize int) error
	Save(values ...*model.Environment) error
	First() (*model.Environment, error)
	Take() (*model.Environment, error)
	Last() (*model.Environment, error)
	Find() ([]*model.Environment, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Environment, err error)
	FindInBatches(result *[]*model.Environment, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Environment) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IEnvironmentDo
	Assign(attrs ...field.AssignExpr) IEnvironmentDo
	Joins(fields ...field.RelationField) IEnvironmentDo
	Preload(fields ...field.RelationField) IEnvironmentDo
	FirstOrInit() (*model.Environment, error)
	FirstOrCreate() (*model.Environment, error)
	FindByPage(offset int, limit int) (result []*model.Environment, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IEnvironmentDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (e environmentDo) Debug() IEnvironmentDo {
	return e.withDO(e.DO.Debug())
}

func (e environmentDo) WithContext(ctx context.Context) IEnvironmentDo {
	return e.withDO(e.DO.WithContext(ctx))
}

func (e environmentDo) ReadDB() IEnvironmentDo {
	return e.Clauses(dbresolver.Read)
}

func (e environmentDo) WriteDB() IEnvironmentDo {
	return e.Clauses(dbresolver.Write)
}

func (e environmentDo) Session(config *gorm.Session) IEnvironmentDo {
	return e.withDO(e.DO.Session(config))
}

func (e environmentDo) Clauses(conds ...clause.Expression) IEnvironmentDo {
	return e.withDO(e.DO.Clauses(conds...))
}

func (e environmentDo) Returning(value interface{}, columns ...string) IEnvironmentDo {
	return e.withDO(e.DO.Returning(value, columns...))
}

func (e environmentDo) Not(conds ...gen.Condition) IEnvironmentDo {
	return e.withDO(e.DO.Not(conds...))
}

func (e environmentDo) Or(conds ...gen.Condition) IEnvironmentDo {
	return e.withDO(e.DO.Or(conds...))
}

func (e environmentDo) Select(conds ...field.Expr) IEnvironmentDo {
	return e.withDO(e.DO.Select(conds...))
}

func (e environmentDo) Where(conds ...gen.Condition) IEnvironmentDo {
	return e.withDO(e.DO.Where(conds...))
}

func (e environmentDo) Order(conds ...field.Expr) IEnvironmentDo {
	return e.withDO(e.DO.Order(conds...))
}

func (e environmentDo) Distinct(cols ...field.Expr) IEnvironmentDo {
	return e.withDO(e.DO.Distinct(cols...))
}

func (e environmentDo) Omit(cols ...field.Expr) IEnvironmentDo {
	return e.withDO(e.DO.Omit(cols...))
}

func (e environmentDo) Join(table schema.Tabler, on ...field.Expr) IEnvironmentDo {
	return e.withDO(e.DO.Join(table, on...))
}

func (e environmentDo) LeftJoin(table schema.Tabler, on ...field.Expr) IEnvironmentDo {
	return e.withDO(e.DO.LeftJoin(table, on...))
}

func (e environmentDo) RightJoin(table schema.Tabler, on ...field.Expr) IEnvironmentDo {
	return e.withDO(e.DO.RightJoin(table, on...))
}

func (e environmentDo) Group(cols ...field.Expr) IEnvironmentDo {
	return e.withDO(e.DO.Group(cols...))
}

func (e environmentDo) Having(conds ...gen.Condition) IEnvironmentDo {
	return e.withDO(e.DO.Having(conds...))
}

func (e environmentDo) Limit(limit int) IEnvironmentDo {
	return e.withDO(e.DO.Limit(limit))
}

func (e environmentDo) Offset(offset int) IEnvironmentDo {
	return e.withDO(e.DO.Offset(offset))
}

func (e environmentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IEnvironmentDo {
	return e.withDO(e.DO.Scopes(funcs...))
}

func (e environmentDo) Unscoped() IEnvironmentDo {
	return e.withDO(e.DO.Unscoped())
}

func (e environmentDo) Create(values ...*model.Environment) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Create(values)
}

func (e environmentDo) CreateInBatches(values []*model.Environment, batchSize int) error {
	return e.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (e environmentDo) Save(values ...*model.Environment) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Save(values)
}

func (e environmentDo) First() (*model.Environment, error) {
	if result, err := e.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Environment), nil
	}
}

func (e environmentDo) Take() (*model.Environment, error) {
	if result, err := e.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Environment), nil
	}
}

func (e environmentDo) Last() (*model.Environment, error) {
	if result, err := e.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Environment), nil
	}
}

func (e environmentDo) Find() ([]*model.Environment, error) {
	result, err := e.DO.Find()
	return result.([]*model.Environment), err
}

func (e environmentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Environment, err error) {
	buf := make([]*model.Environment, 0, batchSize)
	err = e.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (e environmentDo) FindInBatches(result *[]*model.Environment, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return e.DO.FindInBatches(result, batchSize, fc)
}

func (e environmentDo) Attrs(attrs ...field.AssignExpr) IEnvironmentDo {
	return e.withDO(e.DO.Attrs(attrs...))
}

func (e environmentDo) Assign(attrs ...field.AssignExpr) IEnvironmentDo {
	return e.withDO(e.DO.Assign(attrs...))
}

func (e environmentDo) Joins(fields ...field.RelationField) IEnvironmentDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Joins(_f))
	}
	return &e
}

func (e environmentDo) Preload(fields ...field.RelationField) IEnvironmentDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Preload(_f))
	}
	return &e
}

func (e environmentDo) FirstOrInit() (*model.Environment, error) {
	if result, err := e.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Environment), nil
	}
}

func (e environmentDo) FirstOrCreate() (*model.Environment, error) {
	if result, err := e.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Environment), nil
	}
}

func (e environmentDo) FindByPage(offset int, limit int) (result []*model.Environment, count int64, err error) {
	result, err = e.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = e.Offset(-1).Limit(-1).Count()
	return
}

func (e environmentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = e.Count()
	if err != nil {
		return
	}

	err = e.Offset(offset).Limit(limit).Scan(result)
	return
}

func (e environmentDo) Scan(result interface{}) (err error) {
	return e.DO.Scan(result)
}

func (e environmentDo) Delete(models ...*model.Environment) (result gen.ResultInfo, err error) {
	return e.DO.Delete(models)
}

func (e *environmentDo) withDO(do gen.Dao) *environmentDo {
	e.DO = *do.(*gen.DO)
	return e
}
