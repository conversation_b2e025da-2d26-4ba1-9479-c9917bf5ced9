// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameUser = "users"

// User mapped from table <users>
type User struct {
	ID                       int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true;comment:用户 ID，自增主键" json:"id"`                                                      // 用户 ID，自增主键
	UserName                 string     `gorm:"column:user_name;type:character varying(20);not null;comment:用户名，最长 20 字符，唯一" json:"user_name"`                                          // 用户名，最长 20 字符，唯一
	Email                    string     `gorm:"column:email;type:character varying(100);not null;index:idx_users_email,priority:1;comment:用户邮箱，唯一索引" json:"email"`                      // 用户邮箱，唯一索引
	Password                 string     `gorm:"column:password;type:character varying(60);not null;comment:加密密码" json:"password"`                                                       // 加密密码
	Telephone                string     `gorm:"column:telephone;type:character varying(15);not null;index:idx_users_telephone,priority:1;comment:手机号，唯一索引" json:"telephone"`            // 手机号，唯一索引
	IsActive                 *bool      `gorm:"column:is_active;type:boolean;default:true;comment:账户是否启用" json:"is_active"`                                                             // 账户是否启用
	IsDeleted                *bool      `gorm:"column:is_deleted;type:boolean;comment:是否软删除（逻辑删除）" json:"is_deleted"`                                                                   // 是否软删除（逻辑删除）
	IsTwoFactorEnabled       *bool      `gorm:"column:is_two_factor_enabled;type:boolean;comment:是否启用两步验证" json:"is_two_factor_enabled"`                                                // 是否启用两步验证
	TwoFactorSecret          *string    `gorm:"column:two_factor_secret;type:character varying(255);comment:两步验证密钥" json:"two_factor_secret"`                                           // 两步验证密钥
	RealNameType             *int32     `gorm:"column:real_name_type;type:integer;comment:实名认证类型：0 未认证，1 个人，2 企业" json:"real_name_type"`                                                // 实名认证类型：0 未认证，1 个人，2 企业
	RealName                 string     `gorm:"column:real_name;type:character varying(20);not null;comment:用户真实姓名" json:"real_name"`                                                   // 用户真实姓名
	IDCardNumber             string     `gorm:"column:id_card_number;type:character varying(20);not null;comment:身份证号码" json:"id_card_number"`                                          // 身份证号码
	CompanyName              string     `gorm:"column:company_name;type:character varying(50);not null;comment:企业认证名称" json:"company_name"`                                             // 企业认证名称
	CompanyUnifiedSocialCode string     `gorm:"column:company_unified_social_code;type:character varying(18);not null;comment:企业统一社会信用代码" json:"company_unified_social_code"`           // 企业统一社会信用代码
	TeamID                   int32      `gorm:"column:team_id;type:integer;not null;index:idx_users_team_id,priority:1;comment:所属团队 ID" json:"team_id"`                                 // 所属团队 ID
	RoleID                   int32      `gorm:"column:role_id;type:integer;not null;index:idx_users_role_id,priority:1;comment:绑定角色 ID" json:"role_id"`                                 // 绑定角色 ID
	CreatedAt                *time.Time `gorm:"column:created_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`                   // 创建时间
	UpdatedAt                *time.Time `gorm:"column:updated_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`                   // 更新时间
	InviteUserID             *int32     `gorm:"column:invite_user_id;type:integer;comment:邀请用户id" json:"invite_user_id"`                                                                // 邀请用户id
	CommissionType           *int16     `gorm:"column:commission_type;type:smallint;comment:返利类型" json:"commission_type"`                                                               // 返利类型
	CommissionRate           *int16     `gorm:"column:commission_rate;type:smallint;comment:返利比例" json:"commission_rate"`                                                               // 返利比例
	InviteCode               *string    `gorm:"column:invite_code;type:character varying(32);uniqueIndex:idx_users_invite_code,priority:1;comment:用户的邀请码，唯一且用于邀请注册" json:"invite_code"` // 用户的邀请码，唯一且用于邀请注册
}

// TableName User's table name
func (*User) TableName() string {
	return TableNameUser
}
