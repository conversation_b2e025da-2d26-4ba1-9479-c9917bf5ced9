// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameSubscription = "subscriptions"

// Subscription mapped from table <subscriptions>
type Subscription struct {
	ID          int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true" json:"id"`
	Name        string     `gorm:"column:name;type:jsonb;not null;comment:本地化名称，使用 JSONB 存储" json:"name"`                                                // 本地化名称，使用 JSONB 存储
	Storagesize int64      `gorm:"column:storagesize;type:bigint;not null;comment:存储容量限制，单位为字节（Byte）" json:"storagesize"`                                // 存储容量限制，单位为字节（Byte）
	Membercount *int32     `gorm:"column:membercount;type:integer;not null;default:1;comment:成员数量限制" json:"membercount"`                                 // 成员数量限制
	Price       float64    `gorm:"column:price;type:numeric(10,2);not null;comment:价格金额" json:"price"`                                                   // 价格金额
	Currency    string     `gorm:"column:currency;type:character varying(3);not null;comment:币种，ISO 4217 代码，如 CNY, USD, JPY" json:"currency"`            // 币种，ISO 4217 代码，如 CNY, USD, JPY
	CreatedAt   *time.Time `gorm:"column:created_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt   *time.Time `gorm:"column:updated_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"` // 更新时间
}

// TableName Subscription's table name
func (*Subscription) TableName() string {
	return TableNameSubscription
}
