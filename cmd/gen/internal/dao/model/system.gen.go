// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameSystem = "system"

// System mapped from table <system>
type System struct {
	Key         string  `gorm:"column:key;type:text;primaryKey;comment:配置项唯一键" json:"key"`                                                   // 配置项唯一键
	Value       string  `gorm:"column:value;type:text;not null;comment:配置值，存为文本，业务层解析" json:"value"`                                         // 配置值，存为文本，业务层解析
	ValueType   *string `gorm:"column:value_type;type:text;not null;default:string;comment:值类型（string, int, bool, json等）" json:"value_type"` // 值类型（string, int, bool, json等）
	Description *string `gorm:"column:description;type:text;comment:配置项说明备注" json:"description"`                                             // 配置项说明备注
}

// TableName System's table name
func (*System) TableName() string {
	return TableNameSystem
}
