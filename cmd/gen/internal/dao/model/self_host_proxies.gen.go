// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameSelfHostProxy = "self_host_proxies"

// SelfHostProxy mapped from table <self_host_proxies>
type SelfHostProxy struct {
	ID            int32   `gorm:"column:id;type:integer;primaryKey;autoIncrement:true;comment:自增主键 ID" json:"id"`                                                         // 自增主键 ID
	Name          *string `gorm:"column:name;type:character varying(20);comment:代理名称，用户自定义标识" json:"name"`                                                                // 代理名称，用户自定义标识
	Type          int16   `gorm:"column:type;type:smallint;not null;comment:代理协议类型（1 HTTP, 2 HTTPS, 3 SOCKS5）" json:"type"`                                               // 代理协议类型（1 HTTP, 2 HTTPS, 3 SOCKS5）
	Host          *string `gorm:"column:host;type:character varying(255);comment:主机地址，可为 IP 或域名" json:"host"`                                                             // 主机地址，可为 IP 或域名
	Port          int16   `gorm:"column:port;type:smallint;not null;comment:代理服务端口" json:"port"`                                                                          // 代理服务端口
	Username      *string `gorm:"column:username;type:character varying(50);comment:可选的代理用户名" json:"username"`                                                            // 可选的代理用户名
	Password      *string `gorm:"column:password;type:character varying(50);comment:可选的代理密码" json:"password"`                                                             // 可选的代理密码
	TeamID        int32   `gorm:"column:team_id;type:integer;not null;index:idx_selfhostproxy_team_id,priority:1;comment:代理所属团队 ID" json:"team_id"`                       // 代理所属团队 ID
	EnvironmentID int32   `gorm:"column:environment_id;type:integer;not null;index:idx_selfhostproxy_environment_id,priority:1;comment:代理绑定的环境 ID" json:"environment_id"` // 代理绑定的环境 ID
}

// TableName SelfHostProxy's table name
func (*SelfHostProxy) TableName() string {
	return TableNameSelfHostProxy
}
