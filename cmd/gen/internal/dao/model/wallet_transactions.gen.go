// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameWalletTransaction = "wallet_transactions"

// WalletTransaction mapped from table <wallet_transactions>
type WalletTransaction struct {
	ID              int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true;comment:主键，自增交易 ID" json:"id"`                                                     // 主键，自增交易 ID
	UserID          int32      `gorm:"column:user_id;type:integer;not null;index:idx_wallet_user_id,priority:1;comment:用户 ID" json:"user_id"`                                 // 用户 ID
	Amount          int64      `gorm:"column:amount;type:bigint;not null;comment:交易金额，单位为分，正数为收入，负数为支出" json:"amount"`                                                        // 交易金额，单位为分，正数为收入，负数为支出
	Currency        string     `gorm:"column:currency;type:character(3);not null;comment:货币类型，ISO 4217 编码（如 CNY, USD）" json:"currency"`                                       // 货币类型，ISO 4217 编码（如 CNY, USD）
	TransactionType int16      `gorm:"column:transaction_type;type:smallint;not null;comment:交易类型（1充值，2消费，3退款等）" json:"transaction_type"`                                     // 交易类型（1充值，2消费，3退款等）
	ReferenceID     int32      `gorm:"column:reference_id;type:integer;not null;index:idx_wallet_reference_id,priority:1;comment:相关订单或对象的引用 ID，默认 0 表示无" json:"reference_id"` // 相关订单或对象的引用 ID，默认 0 表示无
	Description     string     `gorm:"column:description;type:character varying(100);not null;comment:交易说明，最长 100 字符" json:"description"`                                     // 交易说明，最长 100 字符
	CreatedAt       *time.Time `gorm:"column:created_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:交易记录创建时间" json:"created_at"`              // 交易记录创建时间
}

// TableName WalletTransaction's table name
func (*WalletTransaction) TableName() string {
	return TableNameWalletTransaction
}
