// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameOrder = "orders"

// Order mapped from table <orders>
type Order struct {
	ID            int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true;comment:订单主键 ID" json:"id"`                                                                       // 订单主键 ID
	OrderNumber   string     `gorm:"column:order_number;type:character varying(255);not null;comment:唯一订单号" json:"order_number"`                                                           // 唯一订单号
	UserID        int32      `gorm:"column:user_id;type:integer;not null;index:idx_orders_user_id,priority:1;comment:下单用户 ID" json:"user_id"`                                              // 下单用户 ID
	TeamID        int32      `gorm:"column:team_id;type:integer;not null;index:idx_orders_team_id,priority:1;comment:用户所属团队 ID" json:"team_id"`                                            // 用户所属团队 ID
	Amount        int64      `gorm:"column:amount;type:bigint;not null;comment:应支付金额（单位分）" json:"amount"`                                                                                  // 应支付金额（单位分）
	RealAmount    int64      `gorm:"column:real_amount;type:bigint;not null;comment:实际支付金额（单位分）" json:"real_amount"`                                                                       // 实际支付金额（单位分）
	Currency      string     `gorm:"column:currency;type:character(3);not null;comment:货币类型（ISO 4217）" json:"currency"`                                                                    // 货币类型（ISO 4217）
	Status        int16      `gorm:"column:status;type:smallint;not null;comment:订单状态（1待支付, 2已支付, 3失败, 4取消）" json:"status"`                                                                // 订单状态（1待支付, 2已支付, 3失败, 4取消）
	OrderType     int16      `gorm:"column:order_type;type:smallint;not null;comment:订单类型（1订阅, 2代理, 3充值）" json:"order_type"`                                                               // 订单类型（1订阅, 2代理, 3充值）
	PaymentMethod int16      `gorm:"column:payment_method;type:smallint;not null;comment:支付方式（1余额, 2Stripe, 3支付宝）" json:"payment_method"`                                                  // 支付方式（1余额, 2Stripe, 3支付宝）
	URL           *string    `gorm:"column:url;type:character varying(255);comment:支付网关的支付链接" json:"url"`                                                                                  // 支付网关的支付链接
	OrderContent  *string    `gorm:"column:order_content;type:jsonb;comment:订单详细内容，JSONB 格式" json:"order_content"`                                                                         // 订单详细内容，JSONB 格式
	ExpiresAt     *time.Time `gorm:"column:expires_at;type:timestamp without time zone;index:idx_orders_expires_at,priority:1;default:CURRENT_TIMESTAMP;comment:订单过期时间" json:"expires_at"` // 订单过期时间
	CreatedAt     *time.Time `gorm:"column:created_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:订单创建时间" json:"created_at"`                               // 订单创建时间
	UpdatedAt     *time.Time `gorm:"column:updated_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:订单更新时间" json:"updated_at"`                               // 订单更新时间
	CouponID      *int32     `gorm:"column:coupon_id;type:integer;comment:使用的优惠券ID" json:"coupon_id"`                                                                                      // 使用的优惠券ID
	BalanceAmount int64      `gorm:"column:balance_amount;type:bigint;not null;comment:余额抵扣数量" json:"balance_amount"`                                                                      // 余额抵扣数量
}

// TableName Order's table name
func (*Order) TableName() string {
	return TableNameOrder
}
