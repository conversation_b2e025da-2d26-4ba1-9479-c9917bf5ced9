// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameStaticProxyPricing = "static_proxy_pricing"

// StaticProxyPricing mapped from table <static_proxy_pricing>
type StaticProxyPricing struct {
	ID            int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true" json:"id"`
	CountryCode   string     `gorm:"column:country_code;type:character varying(10);not null;index:idx_proxy_country_code,priority:1" json:"country_code"`
	CountryLocale string     `gorm:"column:country_locale;type:jsonb;not null;index:idx_proxy_country_locale,priority:1" json:"country_locale"`
	City          string     `gorm:"column:city;type:character varying(50);not null;index:idx_proxy_city,priority:1" json:"city"`
	CityLocale    string     `gorm:"column:city_locale;type:jsonb;not null;index:idx_proxy_city_locale,priority:1" json:"city_locale"`
	Carrier       string     `gorm:"column:carrier;type:character varying(50);not null;index:idx_proxy_carrier,priority:1" json:"carrier"`
	CarrierLocale string     `gorm:"column:carrier_locale;type:jsonb;not null;index:idx_proxy_carrier_locale,priority:1" json:"carrier_locale"`
	Price         float64    `gorm:"column:price;type:numeric(10,2);not null" json:"price"`
	Currency      string     `gorm:"column:currency;type:character varying(3);not null" json:"currency"`
	BillingPeriod string     `gorm:"column:billing_period;type:character varying(20);not null" json:"billing_period"`
	IPCount       *int32     `gorm:"column:ip_count;type:integer" json:"ip_count"`
	IsActive      *bool      `gorm:"column:is_active;type:boolean;default:true" json:"is_active"`
	CreatedAt     *time.Time `gorm:"column:created_at;type:timestamp without time zone;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt     *time.Time `gorm:"column:updated_at;type:timestamp without time zone;default:CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName StaticProxyPricing's table name
func (*StaticProxyPricing) TableName() string {
	return TableNameStaticProxyPricing
}
