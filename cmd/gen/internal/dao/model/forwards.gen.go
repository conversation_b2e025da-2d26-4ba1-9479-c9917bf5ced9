// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameForward = "forwards"

// Forward mapped from table <forwards>
type Forward struct {
	ID        int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true;comment:自增 ID" json:"id"`                                               // 自增 ID
	Name      string     `gorm:"column:name;type:character varying(255);not null;comment:服务器名称，用于识别" json:"name"`                                            // 服务器名称，用于识别
	Domain    string     `gorm:"column:domain;type:character varying(255);not null;index:idx_forward_domain_port,priority:1;comment:目标域名或 IP" json:"domain"` // 目标域名或 IP
	Port      int16      `gorm:"column:port;type:smallint;not null;index:idx_forward_domain_port,priority:2;comment:目标端口号" json:"port"`                      // 目标端口号
	CreatedAt *time.Time `gorm:"column:created_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:记录创建时间" json:"created_at"`     // 记录创建时间
	UpdatedAt *time.Time `gorm:"column:updated_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:记录更新时间" json:"updated_at"`     // 记录更新时间
}

// TableName Forward's table name
func (*Forward) TableName() string {
	return TableNameForward
}
