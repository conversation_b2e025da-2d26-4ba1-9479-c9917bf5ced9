// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameNotice = "notices"

// Notice mapped from table <notices>
type Notice struct {
	ID        int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true" json:"id"`
	Title     string     `gorm:"column:title;type:character varying(200);not null;comment:公告标题" json:"title"`                                            // 公告标题
	ImageURL  *string    `gorm:"column:image_url;type:text;comment:公告图片地址（URL）" json:"image_url"`                                                        // 公告图片地址（URL）
	Content   string     `gorm:"column:content;type:text;not null;comment:公告内容，存储为 Markdown 格式文本" json:"content"`                                        // 公告内容，存储为 Markdown 格式文本
	CreatedAt *time.Time `gorm:"column:created_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:公告创建时间" json:"created_at"` // 公告创建时间
	UpdatedAt *time.Time `gorm:"column:updated_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:公告更新时间" json:"updated_at"` // 公告更新时间
}

// TableName Notice's table name
func (*Notice) TableName() string {
	return TableNameNotice
}
