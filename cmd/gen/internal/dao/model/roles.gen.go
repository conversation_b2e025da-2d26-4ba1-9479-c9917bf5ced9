// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameRole = "roles"

// Role mapped from table <roles>
type Role struct {
	ID          int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true;comment:角色 ID，自增主键" json:"id"`                                      // 角色 ID，自增主键
	Name        string     `gorm:"column:name;type:character varying(50);not null;comment:角色名称，最长 50 字符" json:"name"`                                      // 角色名称，最长 50 字符
	TeamID      int32      `gorm:"column:team_id;type:integer;not null;index:idx_role_team_id,priority:1;comment:所属团队 ID" json:"team_id"`                  // 所属团队 ID
	Permissions string     `gorm:"column:permissions;type:jsonb;not null;comment:权限信息，JSONB 格式存储，如访问路径、方法等" json:"permissions"`                            // 权限信息，JSONB 格式存储，如访问路径、方法等
	CreatedAt   *time.Time `gorm:"column:created_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`   // 创建时间
	UpdatedAt   *time.Time `gorm:"column:updated_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:最近更新时间" json:"updated_at"` // 最近更新时间
	Secure      *bool      `gorm:"column:secure;type:boolean;comment:是否开启安全登录" json:"secure"`                                                              // 是否开启安全登录
}

// TableName Role's table name
func (*Role) TableName() string {
	return TableNameRole
}
