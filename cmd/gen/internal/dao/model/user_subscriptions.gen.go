// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameUserSubscription = "user_subscriptions"

// UserSubscription mapped from table <user_subscriptions>
type UserSubscription struct {
	ID           int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true;comment:订阅记录主键 ID" json:"id"`                                     // 订阅记录主键 ID
	OrderID      int32      `gorm:"column:order_id;type:integer;not null;comment:对应订单 ID" json:"order_id"`                                                // 对应订单 ID
	TeamID       int32      `gorm:"column:team_id;type:integer;not null;index:idx_subscription_team_id,priority:1;comment:团队 ID" json:"team_id"`          // 团队 ID
	StartDate    time.Time  `gorm:"column:start_date;type:timestamp without time zone;not null;comment:订阅开始时间" json:"start_date"`                         // 订阅开始时间
	EndDate      time.Time  `gorm:"column:end_date;type:timestamp without time zone;not null;comment:订阅结束时间" json:"end_date"`                             // 订阅结束时间
	StorageSize  int64      `gorm:"column:storage_size;type:bigint;not null;comment:容量限制" json:"storage_size"`                                            // 容量限制
	MembersCount int16      `gorm:"column:members_count;type:smallint;not null;comment:订阅成员数量限制" json:"members_count"`                                    // 订阅成员数量限制
	TotalPrice   int64      `gorm:"column:total_price;type:bigint;not null;comment:购买总价（单位分）" json:"total_price"`                                         // 购买总价（单位分）
	Status       int16      `gorm:"column:status;type:smallint;not null;comment:订阅状态（1: Active, 2: Expired, 3: Cancelled, 4: Pending）" json:"status"`     // 订阅状态（1: Active, 2: Expired, 3: Cancelled, 4: Pending）
	CreatedAt    *time.Time `gorm:"column:created_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt    *time.Time `gorm:"column:updated_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"` // 更新时间
}

// TableName UserSubscription's table name
func (*UserSubscription) TableName() string {
	return TableNameUserSubscription
}
