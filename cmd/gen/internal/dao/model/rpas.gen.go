// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameRpa = "rpas"

// Rpa mapped from table <rpas>
type Rpa struct {
	ID          int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true;comment:RPA配置ID，主键" json:"id"`                                                                           // RPA配置ID，主键
	Name        string     `gorm:"column:name;type:character varying(100);not null;comment:RPA配置名称" json:"name"`                                                                                // RPA配置名称
	Description *string    `gorm:"column:description;type:text;comment:RPA配置备注描述" json:"description"`                                                                                           // RPA配置备注描述
	ConfigFile  string     `gorm:"column:config_file;type:text;not null;comment:YAML格式的配置文件内容" json:"config_file"`                                                                              // YAML格式的配置文件内容
	TeamID      int32      `gorm:"column:team_id;type:integer;not null;index:idx_rpas_team_id,priority:1;comment:所属团队ID" json:"team_id"`                                                        // 所属团队ID
	CreatedBy   int32      `gorm:"column:created_by;type:integer;not null;index:idx_rpas_created_by,priority:1;comment:创建者用户ID" json:"created_by"`                                              // 创建者用户ID
	IsActive    *bool      `gorm:"column:is_active;type:boolean;index:idx_rpas_is_active,priority:1;default:true;comment:是否启用" json:"is_active"`                                                // 是否启用
	IsDeleted   *bool      `gorm:"column:is_deleted;type:boolean;index:idx_rpas_is_deleted,priority:1;comment:是否删除（软删除）" json:"is_deleted"`                                                     // 是否删除（软删除）
	CreatedAt   *time.Time `gorm:"column:created_at;type:timestamp without time zone;not null;index:idx_rpas_created_at,priority:1;default:CURRENT_TIMESTAMP;comment:记录创建时间" json:"created_at"` // 记录创建时间
	UpdatedAt   *time.Time `gorm:"column:updated_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:记录最近更新时间" json:"updated_at"`                                    // 记录最近更新时间
}

// TableName Rpa's table name
func (*Rpa) TableName() string {
	return TableNameRpa
}
