// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameProxy = "proxies"

// Proxy mapped from table <proxies>
type Proxy struct {
	ID            int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true;comment:代理 ID，自增主键" json:"id"`                                                  // 代理 ID，自增主键
	Name          *string    `gorm:"column:name;type:character varying(30);comment:代理名称" json:"name"`                                                                    // 代理名称
	IPAddress     int32      `gorm:"column:ip_address;type:integer;not null;comment:IPv4 地址，整数形式" json:"ip_address"`                                                     // IPv4 地址，整数形式
	Port          int16      `gorm:"column:port;type:smallint;not null;comment:端口号" json:"port"`                                                                         // 端口号
	SocksUsername *string    `gorm:"column:socks_username;type:character varying(30);comment:socks5 登录用户名" json:"socks_username"`                                        // socks5 登录用户名
	SocksPassword *string    `gorm:"column:socks_password;type:character varying(30);comment:socks5 登录密码" json:"socks_password"`                                         // socks5 登录密码
	TeamID        int32      `gorm:"column:team_id;type:integer;not null;index:idx_proxy_team_id,priority:1;comment:绑定的团队 ID" json:"team_id"`                            // 绑定的团队 ID
	EnvironmentID int32      `gorm:"column:environment_id;type:integer;not null;index:idx_proxy_environment_id,priority:1;comment:绑定的环境 ID" json:"environment_id"`       // 绑定的环境 ID
	NodeID        int32      `gorm:"column:node_id;type:integer;not null;index:idx_proxy_node_id,priority:1;comment:代理所属节点" json:"node_id"`                              // 代理所属节点
	Region        string     `gorm:"column:region;type:character varying(100);not null;comment:地区信息" json:"region"`                                                      // 地区信息
	TrafficLimit  int64      `gorm:"column:traffic_limit;type:bigint;not null;comment:最大流量限制（单位：字节）" json:"traffic_limit"`                                               // 最大流量限制（单位：字节）
	Unlimited     *bool      `gorm:"column:unlimited;type:boolean;comment:是否为无限流量" json:"unlimited"`                                                                     // 是否为无限流量
	IsForwarding  *bool      `gorm:"column:is_forwarding;type:boolean;comment:是否启用转发代理" json:"is_forwarding"`                                                            // 是否启用转发代理
	ForwardID     int32      `gorm:"column:forward_id;type:integer;not null;comment:转发配置 ID（Forward 表）" json:"forward_id"`                                               // 转发配置 ID（Forward 表）
	AutoRenew     *bool      `gorm:"column:auto_renew;type:boolean;comment:是否启用自动续费" json:"auto_renew"`                                                                  // 是否启用自动续费
	SpeedLimit    int16      `gorm:"column:speed_limit;type:smallint;not null;comment:限速值，单位可能为 KB/s" json:"speed_limit"`                                                // 限速值，单位可能为 KB/s
	TuicUsername  string     `gorm:"column:tuic_username;type:uuid;not null;index:idx_proxy_tuic_username,priority:1;comment:TUIC 协议使用的 UUID 用户名" json:"tuic_username"`  // TUIC 协议使用的 UUID 用户名
	ExpiresAt     time.Time  `gorm:"column:expires_at;type:timestamp without time zone;not null;index:idx_proxy_expires_at,priority:1;comment:代理过期时间" json:"expires_at"` // 代理过期时间
	CreatedAt     *time.Time `gorm:"column:created_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`               // 创建时间
	UpdatedAt     *time.Time `gorm:"column:updated_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:最近更新时间" json:"updated_at"`             // 最近更新时间
}

// TableName Proxy's table name
func (*Proxy) TableName() string {
	return TableNameProxy
}
