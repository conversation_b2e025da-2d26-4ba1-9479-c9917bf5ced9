// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameEnvironment = "environments"

// Environment mapped from table <environments>
type Environment struct {
	ID         int32          `gorm:"column:id;type:integer;primaryKey;autoIncrement:true;comment:环境ID，自增主键" json:"id"`                                          // 环境ID，自增主键
	TeamID     int32          `gorm:"column:team_id;type:integer;not null;index:idx_environment_team_id,priority:1;comment:所属团队ID" json:"team_id"`               // 所属团队ID
	Name       string         `gorm:"column:name;type:character varying(100);not null;comment:环境名称，100字符以内" json:"name"`                                         // 环境名称，100字符以内
	UserID     int32          `gorm:"column:user_id;type:integer;not null;index:idx_environment_user_id,priority:1;comment:当前分配给的用户ID" json:"user_id"`           // 当前分配给的用户ID
	GroupID    int32          `gorm:"column:group_id;type:integer;not null;index:idx_environment_group_id,priority:1;comment:所属分组ID，关联 Group 表" json:"group_id"` // 所属分组ID，关联 Group 表
	ProxyID    int32          `gorm:"column:proxy_id;type:integer;not null;index:idx_environment_proxy_id,priority:1;comment:代理ID，关联 Proxy 表" json:"proxy_id"`   // 代理ID，关联 Proxy 表
	ProxyType  int16          `gorm:"column:proxy_type;type:smallint;not null;comment:代理类型，1平台代理，2自有代理" json:"proxy_type"`                                       // 代理类型，1平台代理，2自有代理
	Platform   string         `gorm:"column:platform;type:text;not null;comment:默认平台，例如 https://amazon.com/" json:"platform"`                                    // 默认平台，例如 https://amazon.com/
	Parameters string         `gorm:"column:parameters;type:jsonb;not null;comment:环境参数，使用 JSONB 存储" json:"parameters"`                                          // 环境参数，使用 JSONB 存储
	Storage    string         `gorm:"column:storage;type:text;not null;comment:备份路径，例如 AWS S3 地址" json:"storage"`                                                // 备份路径，例如 AWS S3 地址
	CreatedAt  *time.Time     `gorm:"column:created_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`      // 创建时间
	UpdatedAt  *time.Time     `gorm:"column:updated_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`      // 更新时间
	Tag        string         `gorm:"column:tag;type:jsonb;not null;comment:存储环境标签，使用 JSONB 存储" json:"tag"`                                                      // 存储环境标签，使用 JSONB 存储
	Comment    *string        `gorm:"column:comment;type:text;comment:备注" json:"comment"`                                                                        // 备注
	Sort       *int16         `gorm:"column:sort;type:smallint;comment:环境排序" json:"sort"`                                                                        // 环境排序
	Size       *int32         `gorm:"column:size;type:integer;comment:占用空间大小" json:"size"`                                                                       // 占用空间大小
	DeletedAt  gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp without time zone;comment:删除时间" json:"deleted_at"`                                         // 删除时间
}

// TableName Environment's table name
func (*Environment) TableName() string {
	return TableNameEnvironment
}
