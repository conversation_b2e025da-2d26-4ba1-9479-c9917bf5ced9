// cmd/gen/generate.go
package main

import (
	//"fp-browser/internal/model"
	"log"

	"gorm.io/driver/postgres"
	"gorm.io/gen"
	"gorm.io/gorm"
)

//// Querier 动态查询接口
//// 可以在 service 或 repo 层调用 query.Admin.FilterWithNameAndRole(name, role)
//type Querier interface {
//	// SELECT * FROM @@table WHERE user_name = @name{{if role != ""}} AND role = @role{{end}}
//	FilterWithNameAndRole(name, role string) ([]*model.Admin, error)
//}

func main() {
	dsn := "host=************* user=suiyu password=suiyu dbname=suiyu port=5432 sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn))
	if err != nil {
		log.Fatal("无法连接数据库: ", err)
	}

	g := gen.NewGenerator(gen.Config{
		OutPath:           "internal/dao/query", // 查询接口代码生成路径
		ModelPkgPath:      "internal/dao/model", // 模型文件路径
		FieldNullable:     true,
		FieldCoverable:    true,
		FieldWithIndexTag: true,
		FieldWithTypeTag:  true,
		Mode:              gen.WithDefaultQuery | gen.WithQueryInterface | gen.WithoutContext, // 启用默认 query 和接口绑定
	})

	g.UseDB(db)

	g.ApplyBasic(g.GenerateAllTable()...)
	//g.GenerateAllTable()

	//// 应用 Admin 模型
	//adminModel := g.GenerateModel("admins")
	//commissionTransactionModel := g.GenerateModel("commission_transactions")
	//environmentModel := g.GenerateModel("environments")
	//forwardModel := g.GenerateModel("forwards")
	//groupModel := g.GenerateModel("groups")
	//loginLogModel := g.GenerateModel("login_logs")
	//operationLogModel := g.GenerateModel("operation_logs")
	//orderModel := g.GenerateModel("orders")
	//staticProxyPricingModel := g.GenerateModel("static_proxy_pricing")
	//proxiesModel := g.GenerateModel("proxies")
	//roleModel := g.GenerateModel("roles")
	//teamIPWhitelistModel := g.GenerateModel("team_ip_whitelist")
	//selfHostProxiesModel := g.GenerateModel("self_host_proxies")
	//teamModel := g.GenerateModel("teams")
	//userModel := g.GenerateModel("users")
	//subscriptionModel := g.GenerateModel("subscriptions")
	//userSubscriptionModel := g.GenerateModel("user_subscriptions")
	//walletTransactionModel := g.GenerateModel("wallet_transactions")
	//userBalanceModel := g.GenerateModel("user_balance")
	//systemModel := g.GenerateModel("system")
	//
	//// 绑定自定义接口（生成 query/admin.gen.go 中的动态查询方法）
	//g.ApplyInterface(func(Querier) {}, adminModel,
	//	commissionTransactionModel,
	//	environmentModel,
	//	forwardModel,
	//	groupModel,
	//	loginLogModel,
	//	operationLogModel,
	//	orderModel,
	//	staticProxyPricingModel,
	//	proxiesModel,
	//	roleModel,
	//	teamIPWhitelistModel,
	//	selfHostProxiesModel,
	//	teamModel,
	//	userModel,
	//	subscriptionModel,
	//	userSubscriptionModel,
	//	walletTransactionModel,
	//	userBalanceModel,
	//	systemModel)

	// 生成代码
	g.Execute()
}
